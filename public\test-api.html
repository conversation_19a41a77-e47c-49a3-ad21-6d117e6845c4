<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API URL Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API URL Configuration Test</h1>
        
        <div class="info">
            <strong>Purpose:</strong> This page tests if your API URLs are correctly configured for the subdirectory deployment.
        </div>
        
        <h2>Current Configuration:</h2>
        <div id="config-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Expected Base URL:</strong> https://api.niswey.net/rmscrmseries</p>
            <p><strong>Expected API URL:</strong> https://api.niswey.net/rmscrmseries/api/</p>
        </div>
        
        <h2>API Tests:</h2>
        <button onclick="testApiEndpoint('/api/test')">Test /api/test</button>
        <button onclick="testApiEndpoint('/api/hubspot/company-fields?portal_id=123')">Test HubSpot API</button>
        <button onclick="testApiEndpoint('/api/company/rms-fields')">Test RMS Fields API</button>
        <button onclick="testRelativeUrl()">Test Relative URL Resolution</button>
        
        <div id="results"></div>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Click the test buttons above to check API connectivity</li>
            <li>Green results = API endpoint is reachable</li>
            <li>Red results = API endpoint has issues</li>
            <li>If tests fail, check your server configuration</li>
        </ol>
    </div>

    <script>
        // Display current URL info
        document.getElementById('current-url').textContent = window.location.origin + window.location.pathname.replace('/test-api.html', '');
        
        function addResult(message, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }
        
        async function testApiEndpoint(endpoint) {
            try {
                addResult(`Testing ${endpoint}...`, true);
                
                // Construct full URL
                const baseUrl = window.location.origin + window.location.pathname.replace('/test-api.html', '');
                const fullUrl = baseUrl + endpoint;
                
                addResult(`Full URL: ${fullUrl}`, true);
                
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                if (response.ok) {
                    const data = await response.text();
                    addResult(`✅ ${endpoint} - Status: ${response.status} - Response received`, true);
                } else {
                    addResult(`⚠️ ${endpoint} - Status: ${response.status} - ${response.statusText}`, false);
                }
            } catch (error) {
                addResult(`❌ ${endpoint} - Error: ${error.message}`, false);
            }
        }
        
        function testRelativeUrl() {
            const baseUrl = window.location.origin + window.location.pathname.replace('/test-api.html', '');
            addResult(`Base URL resolved to: ${baseUrl}`, true);
            
            // Test how relative URLs would be resolved
            const testUrls = ['/api/test', '/api/hubspot/company-fields', '/api/company/rms-fields'];
            testUrls.forEach(url => {
                const fullUrl = baseUrl + url;
                addResult(`${url} → ${fullUrl}`, true);
            });
        }
        
        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            testRelativeUrl();
        });
    </script>
</body>
</html>
