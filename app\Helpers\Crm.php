<?php

namespace App\Helpers;

use Log;
use Exception;
use Illuminate\Support\Str;
use App\Models\{CrmPortals, CrmCompany, CrmContact, CrmEquipment, CrmEquipmentAll};
use App\Helpers\{Func};

class Crm
{
	static function getPortalInfo($portalId)
	{
		$portalInfo = CrmPortals::where('portal_id', $portalId)->first();
		return $portalInfo ? $portalInfo->toArray() : false;
	}

	static function getToken($portalId)
	{
		$portal = self::getPortalInfo($portalId);
		$base64 = base64_encode('crmSeries.Mobile' . ':' . '(Added From User Secrets)');
		$payload = [
			"grant_type" => 'password',
			"username" => $portal['username'],
			"password" => $portal['password']
		];
		$url = 'https://crmseriesapi.azurewebsites.net/connect/token';
		try {
			$response = Func::request(
				"POST",
				$url,
				[
					'headers' => [
						'Authorization' => 'Basic ' . $base64,
					],
					'form_params' => $payload
				]
			);
			if (isset($response) && (!isset($response->error))) {
				CrmPortals::updateOrCreate(
					['portal_id' => $portalId],
					[
						'portal_id' => $portalId,
						'username' => $portal['username'],
						'password' => $portal['password'],
						'token' => $response->access_token,
						'refresh_token' => $response->refresh_token,
						'expires_in' => $response->expires_in,
						'scope' => $response->scope,
						'created_at' => date('Y-m-d H:i:s'),
						'updated_at' => date('Y-m-d H:i:s')
					]
				);
				// Log::info("[CrmHelper:getToken] Token Saved sucessfully");
			}
		} catch (Exception $e) {
			Log::error("[CrmHelper:getToken] Exception: " . $e->getMessage());
			return false;
		}
	}

	static function refreshToken($portalId)
	{
		$portal = self::getPortalInfo($portalId);
		$base64 = base64_encode('crmSeries.Mobile' . ':' . '(Added From User Secrets)');
		$payload = [
			"grant_type" => 'refresh_token',
			"refresh_token" => $portal['refresh_token'],
		];
		$url = 'https://crmseriesapi.azurewebsites.net/connect/token';
		try {
			$response = Func::request(
				"POST",
				$url,
				[
					'headers' => [
						'Authorization' => 'Basic ' . $base64,
					],
					'form_params' => $payload
				]
			);
			if (isset($response) && (!isset($response->error))) {
				CrmPortals::where('portal_id', $portalId)->update([
					'portal_id' => $portalId,
					'token' => $response->access_token,
					'refresh_token' => $response->refresh_token,
					'expires_in' => $response->expires_in,
					'scope' => $response->scope,
					'updated_at' => date('Y-m-d H:i:s')
				]);
			}
			return true;
		} catch (Exception $e) {
			Log::error("[CrmHelper:refreshToken] Exception: " . $e->getMessage());
			return false;
		}
	}

	static function getCompanies($data,$fetchOnlyOnePage = false)
	{
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		if($expired_time > $portal['expires_in']){
			$portal = self::getToken($data['portalId']);
		}
		$url = 'https://crmseriesapi.azurewebsites.net/api/companies/hubspot/paged';
		$pageNumber = 1;
		$pageSize = 100;

		$allResponses = [];
		do {

			$payload = [
				"FromDate" => $data['fromDate'] ?? "",
				"NoteIds" => $data['NoteIds'],
				"CompanyIds" => $data['CompanyIds'] ?? "",
				"PageNumber" => $pageNumber,
				"PageSize" => $pageSize,
			];

			//remove everything from payload that is empty
			$payload = array_filter($payload);

			try {
				$start = microtime(true);
				$response = Func::request(
					"GET",
					$url,
					[
						'headers' => [
							'Authorization' => 'Bearer ' . $portal['token'],
						],
						'form_params' => $payload
					]
				);
				$duration = microtime(true) - $start;
        		Log::info("[Crm:getCompanies] Page {$pageNumber} fetched in {$duration} seconds");
				
				if ($fetchOnlyOnePage) {
					Log::info("[Crm:getCompanies] Returning only first page as only wanted keys");
					return $response;
				}

				$allResponses[] = $response;
				$pageCount = $response->data->pageCount;
				$pageNumber = $response->data->pageNumber;
				$pageNumber++;


			} catch (Exception $e) {
				Log::error("[Crm:getCompanies] Exception when fetching data: " . $e->getMessage());
				return false;
			}
		} while ($pageNumber <= $pageCount);

		return $allResponses;
	}

	static function getCompanyPrimaryReps($companyId, $portalId = 722284)
	{
		$portal = self::getPortalInfo($portalId);
		$expired_time = time() - strtotime($portal['updated_at']);
		if($expired_time > $portal['expires_in']){
			$portal =  self::getToken($portalId);
		}
		$url = 'https://crmseriesapi.azurewebsites.net/api/companies/hubspot/paged';

		$payload = [
			"CompanyIds" => $companyId
		];
		try {
			$response = Func::request(
				"GET",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					],
					'form_params' => $payload
				]
			);
			if ($response->data->totalItemCount > 0) {
				return $response->data->items[0]->primaryReps;
			}
			return false;
		} catch (Exception $e) {
			Log::error("[Crm:getCompanyPrimaryReps] Exception when fetching data: " . $e->getMessage());
			return false;
		}
	}


	static function getContacts($data,$fetchOnlyOnePage = false)
	{
		
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		if($expired_time > $portal['expires_in']){
			$portal = self::getToken($data['portalId']);
		}
		$url = 'https://crmseriesapi.azurewebsites.net/api/contacts/hubspot/full';
		$pageNumber = 1;
		$pageSize = 100;
		$allResponses = [];
		do {
			$payload = [
				"FromDate" => $data['fromDate'] ?? "",
				"ContactIds" => $data['contactId'],
				"PageNumber" => $pageNumber,
				"PageSize" => $pageSize,
			];
			
			try {
				$start = microtime(true);
				$response = Func::request(
					"GET",
					$url,
					[
						'headers' => [
							'Authorization' => 'Bearer ' . $portal['token'],
						],
						'form_params' => $payload
					]
				);
				$duration = microtime(true) - $start;
        		Log::info("[Crm:getContacts] Page {$pageNumber} fetched in {$duration} seconds");
				
				if ($fetchOnlyOnePage) {
					Log::info("[Crm:getContacts] Returning only first page as only wanted keys");
					return $response;
				}
				$allResponses[] = $response;
				$pageCount = $response->data->pageCount;
				$pageNumber = $response->data->pageNumber;

				$pageNumber++;
			} catch (Exception $e) {
				Log::error("[Crm:getContacts] Exception when fetching data: " . $e->getMessage());
				return false;
			}
		} while ($pageNumber <= $pageCount);

		return $allResponses;
	}

	static function getEquipments($data,$fetchOnlyOnePage = false)
	{
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		if($expired_time > $portal['expires_in']){
			$portal = self::getToken($data['portalId']);
		}
		Log::info("[Crm:getEquipments] Portal info: " . json_encode($portal));
		// self::getToken($data['portalId']);
		$pageNumber = 1;
		do {
			$equipments = array();
			$url = 'https://crmseriesapi.azurewebsites.net/api/equipment/hubspot';
			$payload = [
				"FromDate" => $data['fromDate'] ?? "",
				"PageSize" => 100,
				"PageNumber" => $pageNumber,
				"EquipmentIds" => $data['EquipmentIds'] ?? ""
			];
			$params = http_build_query($payload);
			$url = $url . "?" . $params;
			$start = microtime(true);
			$response = Func::request(
				"GET",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					]
				]
			);
			
			$duration = microtime(true) - $start;
        	Log::info("[Crm:getEquipments] Page {$pageNumber} fetched in {$duration} seconds");
			
			if ($fetchOnlyOnePage) {
				Log::info("[Crm:getEquipments] Returning only first page as only wanted keys");
				return $response;
			}
			try {
				foreach ($response->data->items as $item) {
					$equipmentid = $item->equipmentId;
					if (!empty($item->startDate) && (isset($item->make) && $item->make == "Komatsu")) {
						$equipment = array("equipmentId" => $equipmentid, "payload" => json_encode($item));
						array_push($equipments, $equipment);
					}
				}
				CrmEquipmentAll::upsert($equipments, ['equipmentId'], ['payload']);
				echo "saving equipments for page " . $pageNumber . PHP_EOL;
				$pageNumber++;
			} catch (Exception $e) {
				Log::error("[Crm:getEquipments] Exception when fetching data on line " . __LINE__ . ": " . $e->getMessage());
				return false;
			}
		} while ($pageNumber <= $response->data->pageCount);
		return true;
	}

	static function getEquipmentsByCompany($data)
	{
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		// if($expired_time > $portal['expires_in']){
		self::getToken($data['portalId']);
		// }
		$url = 'https://crmseriesapi.azurewebsites.net/api/equipment/hubspot';
		$payload = [
			"CompanyId" => $data['companyId']
		];
		try {
			$response = Func::request(
				"GET",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					],
					'form_params' => $payload
				]
			);
			return $response;
		} catch (Exception $e) {
			Log::error("[Crm:getEquipmentsByCompany] Exception when fetching data: " . $e->getMessage());
			return false;
		}
	}

	static function getEquipmentsByEquipmentId($data)
	{
		$portal = self::getPortalInfo($data['portal_id']);

		self::getToken($data['portal_id']);

		$url = 'https://crmseriesapi.azurewebsites.net/api/equipment/hubspot';
		$payload = [
			"EquipmentIds" => $data['equipment_id']
		];
		try {
			$response = Func::request(
				"GET",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					],
					'form_params' => $payload
				]
			);
			if (!empty($response->data->items)) {
				return $response->data->items[0];
			} else {
				return false;
			}
		} catch (Exception $e) {
			Log::error("[Crm:getEquipmentsByEquipmentId] Exception when fetching data: " . $e->getMessage());
			return false;
		}
	}

	static function getMarketing($data)
	{
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		// if($expired_time > $portal['expires_in']){
		self::getToken($data['portalId']);
		// }
		$url = 'https://crmseriesapi.azurewebsites.net/api/marketing/record';
		$payload = [
			[
				"recordType" => $data['recordType'],
				"recordId" => $data['recordId'],
				"externalId" => $data['externalId'],
				"externalRecordType" => $data['externalRecordType'],
				"externalProvider" => $data['externalProvider'],
			]
		];

		try {
			$response = Func::request(
				"POST",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					],
					'json' => $payload
				]
			);
			return $response;
		} catch (Exception $e) {
			Log::error("[Crm:getMarketing] Exception when fetching data: " . $e->getMessage());
			return false;
		}
	}

	static function updateContactCrm($data)
	{
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		// if($expired_time > $portal['expires_in']){
		self::getToken($data['portalId']);
		// }
		$url = 'https://crmseriesapi.azurewebsites.net/api/contacts';

		$payload = [
			"firstName" => $data['firstName'],
			"middleName" => $data['middleName'],
			"lastName" => $data['lastName'],
			"phone" => $data['phone'],
			"email" => $data['email']
		];
		try {
			$response = Func::request(
				"POST",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					],
					'form_params' => $payload
				]
			);
			return $response;
		} catch (Exception $e) {
			Log::error("[Crm:updateContactCrm] Exception when fetching data: " . $e->getMessage());
			return false;
		}
	}

	static function updateCompanyCrm($data)
	{
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		// if($expired_time > $portal['expires_in']){
		self::getToken($data['portalId']);
		// }
		$url = 'https://crmseriesapi.azurewebsites.net/api/companies';

		$payload = [
			"companyName" => $data['companyName'],
			"address1" => $data['address1'],
			"city" => $data['city'],
			"state" => $data['state'],
			"email" => $data['email'],
			"phone" => $data['phone']
		];
		try {
			$response = Func::request(
				"POST",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					],
					'form_params' => $payload
				]
			);
			return $response;
		} catch (Exception $e) {
			Log::error("[Crm:updateCompanyCrm] Exception when fetching data: " . $e->getMessage());
			return false;
		}
	}

	static function updateDealCrm($data)
	{
		$portal = self::getPortalInfo($data['portalId']);
		$expired_time = time() - strtotime($portal['updated_at']);
		// if($expired_time > $portal['expires_in']){
		self::getToken($data['portalId']);
		// }
		$url = 'https://crmseriesapi.azurewebsites.net/api/equipment';

		$payload = [
			"description" => $data['description'],
			"startDate" => $data['startDate']
		];
		try {
			$response = Func::request(
				"POST",
				$url,
				[
					'headers' => [
						'Authorization' => 'Bearer ' . $portal['token'],
					],
					'form_params' => $payload
				]
			);
			return $response;
		} catch (Exception $e) {
			Log::error("[Crm:updateDealCrm] Exception when fetching data: " . $e->getMessage());
			return false;
		}
	}

	static function checkCompanyExist($companyId)
	{
		$checkExists = CrmCompany::select('hubspot_company_id')
			->where('crm_company_id', $companyId)->first();
		return $checkExists ? $checkExists['hubspot_company_id'] : false;
	}
	static function checkContactExist($contactId)
	{
		$checkExists = CrmContact::select('hubspot_contact_id')
			->where('crm_contact_id', $contactId)->first();
		return $checkExists ? $checkExists['hubspot_contact_id'] : false;
	}
	static function checkEquipmentExist($contactId)
	{
		$checkExists = CrmEquipment::select('hubspot_deal_id')
			->where('crm_equipment_id', $contactId)->first();
		return $checkExists ? $checkExists['hubspot_deal_id'] : false;
	}
}
