<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hu<PERSON>pot};
use App\Models\{CrmPortals, CrmData, CrmCompany, CrmEquipment, CrmContact, CrmCompanyPrimaryReps, CrmEquipmentAll, CrmOwners, CrmDealFieldMapping};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmEquipmentsCommand extends Command
{

    protected $signature = "crm:equipments";

    protected $description = "Get Crm data for equipments";

    public function handle()
    {
        // $checkFlag = CrmData::select('status')
        //     ->where('type', '=', 'equipment')
        //     ->where('from', '=', 'crm')
        //     ->whereDate('created_at', today())->get();
        // if (!isset($checkFlag[0]->status)) {

            $dealMappingFields = [];
            //Get Deal Mapfields
            $dealMapFields = CrmDealFieldMapping::all();

            if($dealMapFields){
                foreach($dealMapFields as $dealMapField){
                    $dealMappingFields[$dealMapField->hubspot_field] = $dealMapField->rms_field;
                }
            }

            $portals = CrmPortals::first();
            $portal_id = '';
            if($portals){
                $portal_id = $portals->portal_id;
            }

            $data = [];
            $data['EquipmentIds'] = [];
            $data['portalId'] = $portal_id;
            $data['fromDate'] = '2025-01-01';//date("Y-m-d", time() - 86400);
            Log::info("[CrmEquipmentsCommand:handle] command started for date : " . $data['fromDate']);
            $res = Crm::getEquipments($data);

            if ($res) { //equipments successfully updated in db
                Log::info("[CrmEquipmentsCommand:handle] equipments successfully updated in db for date : " . $data['fromDate']);
                $total_updated_data_today = CrmEquipmentAll::whereDate('updated_at', today())->count();
                CrmEquipmentAll::whereDate('updated_at', today())
                    ->chunkById(200, function ($equipments) use ($dealMappingFields,$portal_id) {
                        foreach ($equipments as $items) {
                            $baseItem = $items;
                            $items = $items->payload;
                            $items = json_decode($items);
                            if (!(int)$items->equipmentYear || !preg_match('/^[a-zA-Z0-9-]+$/', $items->serialNumber)) {
                                continue;
                            }

                            if (isset($items->make) && $items->make != "Komatsu") {
                                continue;
                            }
                            if (empty($items->startDate)) {
                                continue;
                            }

                            Log::info("[CrmEquipmentsCommand:handle] if inventory is not true, equipments successfully created in db, CrmEquipmentAll: " . $baseItem->id);

                            //EquipmentYear - Make - Model - SerialNumber - CompanyName
                            $name = (isset($items->parentName) || isset($items->make) || isset($items->model) || isset($items->serialNumber) || isset($items->equipmentYear))
                                ? ($items->equipmentYear ?? "") . "" . ' - ' . ($items->make ?? "") . "" . ' - ' . ($items->model ?? "") . "" . ' - ' . ($items->serialNumber ?? "") . "" . ' - ' . ($items->parentName ?? "")
                                : "-";
                            $startDate = ($items->startDate !== NULL) ? strtotime($items->startDate) * 1000 : "";
                            $lastSmrDate = ($items->lastSmrDate !== NULL) ? strtotime($items->lastSmrDate) * 1000 : "";
                            $stdWarrantyEndDate = "";
                            $extWarrantyEndDate = "";
                            $stdWarrantyHours = "";
                            $extWarrantyHours = "";
                            $leaseExpire = "";
                            $tier = "";
                            $warrantyTypeArr = [];
                            $wType = "";
                            $equipmentId = $items->equipmentId ?? "";
                            $companyId = $items->parentId ?? "";
                            $hubspotCompanyId = "";
                            $hubspotContactId = "";
                            //Get company rep from API with companyID. Fetch hubspot_owner_id from db with email. Create DB for HS ownerID and email
                            // $get_owner = CrmCompanyPrimaryReps::select('hubspot_owner_id')
                            //     ->where('company_id', $companyId)->first();
                            //$deal_owner = $get_owner['hubspot_owner_id'] ?? "";
                            $deal_owner = "";
                            if (!empty($companyId)) {
                                $getCompanyPrimaryReps = Crm::getCompanyPrimaryReps($companyId,$portal_id);
                                if (!empty($getCompanyPrimaryReps)) {
                                    foreach ($getCompanyPrimaryReps as $companyPrimaryReps) {
                                        $owner_email = $companyPrimaryReps->email;
                                        $role = $companyPrimaryReps->role;
                                        if ($role == "Komatsu Rep") {
                                            $owner = CrmOwners::where('email', $owner_email)->first();
                                            if (isset($owner->owner_id)) {
                                                $deal_owner = $owner->owner_id;
                                            }
                                            break;
                                        }
                                    }
                                }
                                $deal_owner = '78856535';
                                Log::info('owner id: '.$deal_owner);
                                $getCompanyData = CrmCompany::select('hubspot_company_id')
                                    ->where('crm_company_id', '=', $companyId)
                                    ->first();
                                $hubspotCompanyId = $getCompanyData['hubspot_company_id'] ?? "";
                                if (!empty($hubspotCompanyId)) {
                                    $getContactData = CrmContact::select('hubspot_contact_id')
                                        ->where('crm_company_id', '=', $companyId)
                                        ->first();
                                    $hubspotContactId = $getContactData['hubspot_contact_id'] ?? "";
                                }
                            }

                            foreach ($items->warrenties as $warranties) {
                                if (!in_array($warranties->warrantyType, ['BS', 'E1', 'E4', 'EP', 'EQ', 'EX', 'FL', 'PR', 'PT', 'QX', 'S', 'S1', 'SD', 'ST', 'U4', 'UN', 'UQ', 'X!', 'X$', 'X1', 'X4', 'XO', 'XQ', 'Y1', 'Y4', 'YQ'])) { //if not correct warrant type skip
                                    continue;
                                }
                                if ($warranties->warrantyType == 'ST') {
                                    $stdWarrantyEndDate = ($warranties->endDate !== NULL) ? strtotime($warranties->endDate) * 1000 : "";
                                    $stdWarrantyHours = ($warranties->hours !== 0) ? $warranties->hours : 0;
                                } else {
                                    $extWarrantyEndDate = ($warranties->endDate !== NULL) ? strtotime($warranties->endDate) * 1000 : "";
                                    $extWarrantyHours = ($warranties->hours !== 0) ? $warranties->hours : 0;
                                }
                                array_push($warrantyTypeArr, $warranties->warrantyType);
                            }
                            foreach ($items->udfs as $udfs) {
                                if ($udfs->fieldName == 'LeaseExpire') {
                                    $leaseExpire = ($udfs->fieldValues[0] !== NULL) ? strtotime($udfs->fieldValues[0]) * 1000 : "";
                                }
                                if ($udfs->fieldName == 'Tier') {
                                    $tier = ($udfs->fieldValues[0] !== NULL) ? $udfs->fieldValues[0] : "";
                                }
                            }
                            $wType = (count($warrantyTypeArr) > 1) ? implode(";", $warrantyTypeArr) : implode("", $warrantyTypeArr);
                            $staticArray = array(
                                'dealname' => $name,
                                'hubspot_owner_id' => $deal_owner,
                                'tier' => $tier,
                            );

                            $props = Hubspot::buildDealPropsFromMapping($dealMappingFields, $items,$staticArray);

                            // $props = Hubspot::buildDealProps([
                            //     'equipment_id' => $items->equipmentId ?? "",
                            //     'parent_name' => $items->parentName ?? "",
                            //     'hubspot_id' => $items->hubspotId ?? "",
                            //     'description' => $items->description ?? "",
                            //     'start_date___date_picker' => $startDate ?? "",
                            //     'start_hours___numeric' => $items->startHours ?? "",
                            //     'lastsmrdate___date_picker' => $lastSmrDate ?? "",
                            //     'lastsmr_date___numeric' => $items->lastSmr ?? "",
                            //     'latitude' => $items->latitude ?? "",
                            //     'longitude' => $items->longitude ?? "",
                            //     'hours_per_day' => $items->hoursPerDay ?? "",
                            //     'hours_per_day_range' => $items->hoursPerDayRange ?? "",
                            //     'inventory' => $items->inventory ?? "",
                            //     'fleet' => $items->fleet ?? "",
                            //     'fleet_type' => $items->fleetType ?? "",
                            //     'status' => $items->status ?? "",
                            //     'new_used' => $items->newUsed ?? "",
                            //     'cbgrp' => $items->groupCode ?? "",
                            //     'hours_per_day_override' => $items->hoursPerDayOverride ?? "",
                            //     'uc_target_wear_percent' => $items->ucTargetWearPercent ?? "",
                            //     'exclude_uc_tracking' => $items->excludeUCTracking ?? "",
                            //     'complete' => $items->complete ?? "",
                            //     'satellite_identifier' => $items->satelliteIdentifier ?? "",
                            //     'parent_id' => $items->parentId ?? "",
                            //     'parent_type' => $items->parentType ?? "",
                            //     'cbmod' => $items->model ?? "",
                            //     'cbmyr' => $items->equipmentYear ?? "",
                            //     'cbmak' => $items->make ?? "",
                            //     'serial_no_' => $items->serialNumber ?? "",
                            //     'cbord' => $items->stockNumber ?? "",
                            //     'equipment_number' => $items->equipmentNumber ?? "",
                            //     'category_id' => $items->categoryId ?? "",
                            //     'stdwtdenddate' => $stdWarrantyEndDate,
                            //     'extwarrantyenddate' => $extWarrantyEndDate,
                            //     'warranty_type___multi_select' => $wType ?? "",
                            //     'basicwarrantyhrs' => $stdWarrantyHours,
                            //     'extwarrantyhrs' => $extWarrantyHours,
                            //     'lease_expiry_date___date_picker' => $leaseExpire ?? "",
                            //     'tier' => $tier,
                            //     'dealname' => $name,
                            //     'hubspot_owner_id' => $deal_owner
                            // ]);

                            $checkequipment = CrmEquipment::where('crm_equipment_id', $equipmentId)->first();
                            if (isset($checkequipment->hubspot_deal_id)
                            && Hubspot::dealExists($portal_id, $checkequipment->hubspot_deal_id)) {
                                $dealid = $checkequipment->hubspot_deal_id;
                                // if (!empty($lastSmrDate)) {
                                //     $response = Hubspot::updateDeal(722284, $dealid, $props); //update only when lastsmr is not empty
                                // } else {
                                //     continue;
                                // }
                                $response = Hubspot::updateDeal($portal_id, $dealid, $props);
                            } else {
                                $response = Hubspot::createDeal($portal_id, $props);
                            }

                            if ($response === false) {
                                Log::error("[CrmEquipmentsCommand:handle] Deal creation/updation failed for equipment id $equipmentId]");
                                // return false;
                            } else {
                                try {
                                    CrmEquipment::updateOrCreate(
                                        [
                                            'crm_equipment_id' => $equipmentId,
                                            'hubspot_deal_id' => $response->dealId,
                                        ],
                                        [
                                            'crm_equipment_id' => $equipmentId,
                                            'hubspot_deal_id' => $response->dealId,
                                            'deal_owner_id' => $deal_owner,
                                            'payload' => json_encode($items),
                                            'updated_at' => date('Y-m-d H:i:s'),
                                        ]
                                    );
                                } catch (Exception $e) {
                                    Log::error("[CrmEquipmentsCommand:handle] Exception: " . $e->getMessage());
                                }
                                if ($hubspotCompanyId) {
                                    list($getAssociationsIds, $formatInputs) = Hubspot::getAssociations($response->dealId, 'deals', 'companies');
                                    if (isset($getAssociationsIds)) {
                                        Hubspot::deleteAssociations($formatInputs, 'deals', 'companies');
                                    }
                                    $connectCompanyToDeal = Hubspot::connectCompanyToDeal($portal_id, $response->dealId, $hubspotCompanyId);
                                }
                                if ($hubspotContactId) {
                                    $connectContactToDeal = Hubspot::connectContactToDeal($portal_id, $response->dealId, $hubspotContactId);
                                }
                            }
                        }
                    }, $column = 'id');
            } else {
                Log::info("[CrmEquipmentsCommand:handle] data not updated today");
                $total_updated_data_today = 0;
            }
            try {
                $status = 0;
                if ($res) {
                    $status = 1;
                }
                CrmData::updateOrCreate(
                    [
                        'created_at' => date('Y-m-d'),
                        'type' => 'equipment',
                    ],
                    [
                        'portal_id' => $portal_id,
                        'type' => 'equipment',
                        'from' => 'crm',
                        'data' => $total_updated_data_today,
                        'created_at' => date('Y-m-d'),
                        'status' => $status
                    ]
                );
            } catch (Exception $e) {
                Log::error("[CrmEquipmentsCommand:handle] Exception: " . $e->getMessage());
            }
            Log::info("[CrmEquipmentCommand:handle] command successfull for date : " . $data['fromDate']);
        }
    // }
}
