<template>
  <DefaultLayout>
    <!-- Loading State -->
    <div v-if="authLoading" class="p-6 max-w-5xl mx-auto">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-gray-600">Checking access permissions...</p>
      </div>
    </div>

    <!-- Access Denied State -->
    <div v-else-if="!canAccessWizard && !authLoading" class="p-6 max-w-5xl mx-auto">
      <div class="text-center">
        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
          <div class="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-red-800 mb-2">Access Denied</h3>
          <p class="text-red-700 mb-4">
            You need Admin role to access the wizard.
            <span v-if="user">Your current role is: <strong>{{ user.role || 'Not assigned' }}</strong></span>
          </p>
          <p class="text-sm text-red-600 mb-4" v-if="authError">{{ authError }}</p>
          <button
            @click="goToAuth"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition"
          >
            Go to Authorization
          </button>
        </div>
      </div>
    </div>

    <!-- Main Wizard Content -->
    <div v-else class="p-6 max-w-5xl mx-auto">
      <div class="text-center mb-8">
        <img src="images/niswey-hubspot.png" alt="Niswey HubSpot Integration" class="mx-auto h-12 mb-2" />
        <h2 class="text-xl font-semibold text-gray-700">
          Connecting <span class="font-bold text-gray-600">RMS</span> to <span class="text-gray-600">HubSpot</span>
        </h2>
      </div>
    <div class="flex space-x-8">

      <!-- Step Indicators -->
      <div class="w-1/5 relative">
        <ul class="text-sm font-medium relative">
          <li
            v-for="(step, index) in steps"
            :key="index"
            class="relative flex space-x-2"
          >
            <!-- Line connecting to the next dot -->
            <div class="flex flex-col items-center z-0 mt-1">
              <!-- Dot -->
              <div
                class="w-3 h-3 rounded-full"
                :class="[
                  currentStep >= index ? 'bg-blue-600' : 'bg-gray-300'
                ]"
              ></div>

              <!-- Vertical Line (only if not last step) -->
              <div
                v-if="index < steps.length - 1"
                class="h-8 w-1"
                :class="currentStep > index ? 'bg-blue-600' : 'bg-gray-300'"
              ></div>
            </div>

            <!-- Step Label -->
            <div  :class="currentStep === index ? 'text-blue-600 font-semibold' : 'text-gray-500'">
              {{ step.label }}
            </div>
          </li>
        </ul>
      </div>


      <!-- Step Component -->
      <div class="flex-1 bg-white rounded-lg shadow p-6 transition-all duration-300">
        <component
          :is="steps[currentStep].component"
          v-model="formState[steps[currentStep].key]"
          @next="nextStep"
          @back="prevStep"
          :is-final="isFinalStep"
          @submit="handleSubmit"
        />
      </div>
    </div>
  </div>
  </DefaultLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import DefaultLayout from '../layouts/DefaultLayout.vue';
import StepUserMapping from '../components/steps/StepUserMapping.vue'
import StepCompanyField from '../components/steps/StepCompanyField.vue'
import StepContactField from '../components/steps/StepContactField.vue'
import StepDealField from '../components/steps/StepDealField.vue'
import { useAuth } from '../composables/useAuth'

// Authentication
const {
  user,
  authLoading,
  authError,
  canAccessWizard,
  checkUserAccess,
  redirectToAuth
} = useAuth()

// Check authentication on component mount
onMounted(async () => {
  console.log('Wizard component mounted, checking user access...')
  const result = await checkUserAccess()

  if (!result.success) {
    console.error('Authentication failed:', result.error)
    // The component will show the access denied state
  } else if (!result.hasAccess) {
    console.warn('User authenticated but lacks admin access')
    // The component will show the access denied state
  } else {
    console.log('User has admin access, proceeding with wizard')
  }
})

// Navigation helper
const goToAuth = () => {
  redirectToAuth()
}

const steps = [
  { label: 'Authorization' },
  { label: 'User mapping', component: StepUserMapping },
  { label: 'Company field mapping', component: StepCompanyField },
  { label: 'Contact field mapping', component: StepContactField },
  { label: 'Deal field mapping', component: StepDealField }
]

const currentStep = ref(1)
const isFinalStep = computed(() => currentStep.value === steps.length - 1)

const formState = ref({
  users: [],
  company: {},
  contact: {},
  deal: {}
})

const nextStep = () => {
  if (currentStep.value < steps.length - 1) currentStep.value++
}

const prevStep = () => {
  if (currentStep.value > 0) currentStep.value--
}

const handleSubmit = () => {
  console.log('🎉 Final Submission:', formState.value)
  alert('Form submitted successfully!')
}


</script>
