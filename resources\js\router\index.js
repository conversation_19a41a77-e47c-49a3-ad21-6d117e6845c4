import { createRouter, createWebHistory } from 'vue-router';
import Auth from '../pages/Auth.vue';
import Wizard from '../pages/Wizard.vue';
import Success from '../pages/Success.vue';
import Error from '../pages/Error.vue';

const routes = [
    { path: '/', redirect: '/auth' },
    { path: '/auth', component: Auth },
    {
        path: '/wizard',
        component: Wizard,
        meta: { requiresAuth: true, requiresAdmin: true }
    },
    { path: '/success', component: Success },
    { path: '/error', component: Error },
];

const router = createRouter({
    history: createWebHistory(process.env.VUE_ROUTER_BASE || '/'),
    routes,
});

// Global navigation guard for authentication
router.beforeEach(async (to, from, next) => {
    // Check if route requires authentication
    if (to.meta.requiresAuth) {
        const urlParams = new URLSearchParams(window.location.search);
        const portalId = urlParams.get('portal_id');
        const userId = urlParams.get('user_id');

        // Check if required URL parameters are present
        if (!portalId || !userId) {
            console.warn('Missing authentication parameters, redirecting to auth');
            next('/auth');
            return;
        }
    }

    next();
});

export default router;
