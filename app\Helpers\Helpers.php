<?php 

if (!function_exists('utf8_converter')) {
    function utf8_converter($array) {
		array_walk_recursive($array, function(&$item, $key){
			if(!mb_detect_encoding($item, 'utf-8', true)){
				$item = utf8_encode($item);
			}
		});
		return $array;
	}
}


if(!function_exists('id2portal')) {
	function id2portal($user_id) {
		$user = explode("|", base64_decode($user_id));
		return $user[1] ?? false;
	}
}

if(!function_exists('id2email')) {
	function id2email($user_id) {
		$user = explode("|", base64_decode($user_id));
		return $user[0] ?? false;
	}
}

if(!function_exists('simple_crypt')) {
	function simple_crypt($action, $string) {
	    $secret_key = 'hubchat';
	    $secret_iv = 'simple';
	 
	    $output = false;
	    $encrypt_method = "AES-256-CBC";
	    $key = hash( 'sha256', $secret_key );
	    $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );
	 
	    if( $action == 'e' ) {
	        $output = base64_encode( openssl_encrypt( $string, $encrypt_method, $key, 0, $iv ) );
	    }
	    else if( $action == 'd' ){
	        $output = openssl_decrypt( base64_decode( $string ), $encrypt_method, $key, 0, $iv );
	    }
	    return $output;
	}
}

if(!function_exists('res')) {
	function res($status, $message, $data = []) {
		$response = ['status' => $status];
		if($message) $response['message'] = $message;
		if($data) $response[$data[0]] = $data[1];

		return response()->json($response);
	}
}

if(!function_exists('dialogId')) {
	function dialogId($user) {
		return $user['portal_id'].'.'.$user['instance_id'];
	}
}

if(!function_exists('timeHash')) {
	function timeHash() {
		return simple_crypt('e', time());
	}
}

if(!function_exists('daysBetween')) {
	function daysBetween($dt1, $dt2) {
	    return date_diff(
	        date_create($dt2),  
	        date_create($dt1)
	    )->format('%a');
	}
}
