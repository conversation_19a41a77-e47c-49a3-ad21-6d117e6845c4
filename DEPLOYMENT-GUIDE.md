# Deployment Guide for RMS CRM Series

## Issues Fixed

The blank page and API URL issues were caused by several configuration problems:

1. **Asset Path Issues**: Assets were being loaded from incorrect paths
2. **Vue Router Base Path**: Router wasn't configured for subdirectory deployment
3. **API URL Configuration**: API calls were going to `/api/` instead of `/rmscrmseries/api/`
4. **Environment Configuration**: Missing production environment variables
5. **Apache .htaccess**: Missing RewriteBase for subdirectory

## Files Modified

1. `webpack.mix.js` - Added proper asset path configuration and environment variables
2. `resources/js/router/index.js` - Added base path support
3. `resources/js/bootstrap.js` - Configured axios with correct base URL
4. `resources/js/composables/useApi.js` - Added base URL handling for fetch API
5. `public/.htaccess` - Added RewriteBase for subdirectory
6. `.env.production` - Created production environment file with correct API URLs
7. `.env` - Added VUE_ROUTER_BASE variable

## Deployment Steps

### Step 1: Build for Production

Run the build script:
```bash
# On Windows
build-production.bat

# On Linux/Mac
chmod +x build-production.sh
./build-production.sh
```

### Step 2: Upload Files

Upload the entire project to your server, ensuring the web server document root points to the `public` directory.

### Step 3: Server Configuration

#### For Apache (recommended)
Make sure your virtual host or directory configuration points to the `public` folder:

```apache
DocumentRoot /path/to/your/project/public
# OR for subdirectory
Alias /rmscrmseries /path/to/your/project/public
```

#### For Nginx
```nginx
server {
    listen 80;
    server_name api.niswey.net;
    root /path/to/your/project/public;
    
    location /rmscrmseries {
        alias /path/to/your/project/public;
        try_files $uri $uri/ /rmscrmseries/index.php?$query_string;
        
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
        }
    }
}
```

### Step 4: File Permissions

Set proper permissions:
```bash
# Make directories writable
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# Make files readable
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
```

### Step 5: Environment Setup

1. Copy `.env.production` to `.env` on the server
2. Update database credentials in `.env`
3. Generate application key if needed:
   ```bash
   php artisan key:generate
   ```

### Step 6: Database Setup

Run migrations if needed:
```bash
php artisan migrate
```

## Testing

After deployment, test these URLs in order:

### 1. Basic File Serving
- `https://api.niswey.net/rmscrmseries/test.html` - Should show test page
- `https://api.niswey.net/rmscrmseries/phpinfo.php` - Should show PHP info

### 2. API URL Testing
- `https://api.niswey.net/rmscrmseries/test-api.html` - Interactive API URL tester

### 3. Application Testing
- `https://api.niswey.net/rmscrmseries/` - Should load the Vue app
- `https://api.niswey.net/rmscrmseries/auth` - Should load auth page
- `https://api.niswey.net/rmscrmseries/api/test` - Should work for API calls

## Troubleshooting

### Still seeing blank page?
1. Check browser console for JavaScript errors
2. Verify asset files are loading (check Network tab)
3. Check Laravel logs: `storage/logs/laravel.log`
4. Verify .htaccess is working (test with a simple HTML file)

### Assets not loading?
1. Verify the `public` directory is the web root
2. Check file permissions
3. Clear browser cache
4. Rebuild assets: `npm run production`

### API calls failing?
1. Check if Laravel routes are working: `/rmscrmseries/test`
2. Verify database connection
3. Check CORS settings if needed

## Production Environment Variables

Key variables in `.env.production`:
- `APP_URL=https://api.niswey.net/rmscrmseries`
- `APP_PUBLIC_PATH=/rmscrmseries/`
- `VUE_ROUTER_BASE=/rmscrmseries/`
- `VUE_PUBLIC_PATH=/rmscrmseries/`

These ensure all paths work correctly in the subdirectory deployment.
