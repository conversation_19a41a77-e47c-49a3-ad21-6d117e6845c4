<?php 
/*  Not Bound with any integration app,
	App, model name can be passed to this class's constructor, 
*/
namespace App\Helpers;

use Log;
use Exception;
use App\Helpers\Func;
use App\Models\DdupStats;

class HubspotApp {

	public $app;

	public $hsApp;

	protected $model;

	function __construct($appName, $model) {
		$this->app = $appName;
		$this->hsApp = config('hsapp.'.$appName);
		$this->model = $model;
	}

	public function createTokens($code) {
		$hsApp = $this->hsApp['auth'];
		$hsApp['code'] = $code;

		$tokens = Func::request('POST', 'https://api.hubapi.com/oauth/v1/token', [
			'form_params' => $hsApp
		]);
		return $tokens;
	}

	public function refreshToken($tokens) {
		$hsApp = $this->hsApp['auth'];
		$hsApp['grant_type'] = 'refresh_token';
		$hsApp['refresh_token'] = $tokens['refresh_token'];
		unset($hsApp['code']);
		unset($hsApp['redirect_uri']);

		$newTokens = Func::request('POST','https://api.hubapi.com/oauth/v1/token',
			['form_params' => $hsApp]
		);
		if(isset($newTokens->status)) {
			$newTokens->refresh_token = $tokens['refresh_token'];
			Log::error("[HubspotApp:refreshToken] Error: ".json_encode($newTokens));
			return $tokens;
		}

		if(isset($newTokens->access_token)) {
			$this->model::where('portal_id', 'like', "$tokens[portal_id]%")->update(['access_token' => $newTokens->access_token, 'updated_at' => date("Y-m-d H:i:s")]);
			$tokens['access_token'] = $newTokens->access_token;
		}
		return $tokens;
	}

	public function getTokens($portal_id) {
		if($this->app == 'infobip' && strlen($portal_id) > 7) {
			$portal_id = substr($portal_id, 0, -1);
		}
		$tokens = $this->model::where('portal_id', 'like', "$portal_id%")->first();
		if($tokens) $tokens = $tokens->toArray();
		$passed_time = time() - strtotime($tokens['updated_at']);
		return $passed_time > 21600 ? $this->refreshToken($tokens) : $tokens;
	}

	public function getPortalId($access_token) {
		return Func::request('GET', 'https://api.hubapi.com/integrations/v1/me', [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $access_token"
			]
		]);
	}

	public function getContact($objectId, $portal_id) {
		$tokens = $this->getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/vid/$objectId/profile";
		
		$contact = Func::request('GET', $hub_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"] 
			]
		);
		if(isset($contact->status)) {
			Log::error("[getContact] for portal_id: $portal_id ".json_encode($contact));
		}
		return isset($contact->vid) ? $contact : false;
	}

	public function search($portal_id, $q) {
		$tokens = $this->getTokens($portal_id);
		$contact = Func::request("GET", "https://api.hubapi.com/contacts/v1/search/query?q=".$q, [
			'headers' => [
			'Content-Type' => 'application/json',
			"Authorization" => "Bearer $tokens[access_token]"]
		]);
		if(isset($contact->status)) {
			Log::error("[search] ".json_encode($contact));
		}
		return isset($contact->contacts) ? $contact->contacts : false;
	}

	public function searchV3($portal_id, $q, $filter = false) {
		$users = [];
		$tokens = self::getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/crm/v3/objects/contacts/search";

		$res = Func::request('POST', $hub_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'json' => $q
		]);

		if(isset($res->status)) {
			Log::error("[HubspotApp:searchV3] Error for $portal_id:".json_encode($res));
			return false;
		}

		$results = $res->results ?? [];
		if(!$filter || !$results) return $results;

		foreach ($results as $contact) {
			$firstname = $contact->properties->firstname ?? "";
			$lastname = $contact->properties->lastname ?? "";
			$fullname = $lastname ? $firstname.' '.$lastname : $firstname;

			$users[] = [
				'name' => $firstname,
				'fullname' => $fullname,
				'email' => $contact->properties->email ?? '',
				'owner_id' => $contact->properties->hubspot_owner_id ?? '',
				'objectId' => $contact->id
			];
		}
		return $users;
	}

	public function mergeContacts($contactToMerge, $mergeToContact, $portal_id, $oldToNew = false) {
		$tokens = $this->getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/merge-vids/".$mergeToContact->vid."/";

		Log::info("[HubspotApp:mergeContacts] request for merge called");
		$response = Func::request('POST', $hub_url, [
			'json' => [
				'vidToMerge' => $contactToMerge->vid
			],
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"]
		]);
		if(isset($response->status)) {
			Log::error("[mergeContacts] ".json_encode($response));
			return false;
		}
		// update properties and save to list
		$toUpdate = [
			'merged_contact_id' => $contactToMerge->vid, 
			'merged_lead_source' => $contactToMerge->properties->lead_source->value ?? '' ,
			'merged_lifecycle_stage' => $contactToMerge->properties->lifecyclestage->value ?? '',
			'lead_source' => $mergeToContact->properties->lead_source->value ?? ''
		];

		if($oldToNew) {
			$toUpdate['hs_lead_status'] = $mergeToContact->properties->hs_lead_status->value ?? '';
			$toUpdate['hs_lifecyclestage_salesqualifiedlead_date'] = $mergeToContact->properties->hs_lifecyclestage_salesqualifiedlead_date->value ?? '';
			$toUpdate['hs_lifecyclestage_opportunity_date'] = $mergeToContact->properties->hs_lifecyclestage_opportunity_date->value ?? '';
			$toUpdate['hubspot_owner_id'] = $mergeToContact->properties->hubspot_owner_id->value ?? '';
			$toUpdate['appointment_date'] = $mergeToContact->properties->appointment_date->value ?? '';
			$toUpdate['center'] = $mergeToContact->properties->center->value ?? '';
			$toUpdate['visit'] = $mergeToContact->properties->visit->value ?? '';
		}

		$props['properties'] = [];
		foreach ($toUpdate as $key => $value) {
			$props['properties'][] = [
				'property' => $key,
				'value' => $value
			];
		}

		Log::info("props: ".json_encode($props));
		$updated = $this->updateProperties($mergeToContact->vid, $props, $portal_id);
		if(!$updated) {
			Log::error("[hubspotApp:mergeToContact] unable to updateProperties");
			// return false;
		}

		$addedToList = $this->addToList($mergeToContact->vid, 1008, $portal_id);
		if(!$addedToList) {
			Log::error("[hubspotApp:mergeToContact] unable to addedToList");
			// return false;
		}
		return $this->saveMergeInfo($contactToMerge, $mergeToContact, $portal_id);
	}

	public function updateProperties($vid, $properties, $portalId) {
		$tokens = $this->getTokens($portalId);
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/vid/".$vid."/profile";
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'body' => $body
		];
		$response =  Func::request('POST', $hub_url, $data); // works if no response
		return $response ? false : true;
	}

	public function addToList($vid, $listId, $portalId) {
		$tokens = $this->getTokens($portalId);
		$hub_url = "https://api.hubapi.com/contacts/v1/lists/$listId/add";
		$list_contacts['vids'] = [$vid];
		$body = json_encode($list_contacts, JSON_UNESCAPED_UNICODE);
		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'body' => $body
		];

		$res = Func::request("POST", $hub_url, $data);
		if(isset($res->updated) || isset($res->discarded)) {
			return true;
		}
		Log::error("[hubspotApp:addedToList] payload".json_encode($res));
		return false;
	}

	public function saveMergeInfo($contactToMerge, $mergeToContact, $portal_id) {
		$newMergedContact = $this->getContact($mergeToContact->vid, $portal_id);
		if(!$newMergedContact) Log::error("[HubspotApp:saveMergeInfo] unable to fetch new: ".json_encode($mergeToContact));

		try {
			$newSource = $newMergedContact->properties->lead_source->value ?? '';
			DdupStats::insert([
				'portal_id' => $portal_id,
				'vidToMerge' => $contactToMerge->vid,
				'old_source' => $mergeToContact->properties->lead_source->value ?? '',
				'mergeTo' => $mergeToContact->vid,
				'new_source' => $newSource ? $newSource: 'X',
				'mergeDump' => json_encode($contactToMerge),
			]);
			return true;
		} catch(Exception $e) {
			Log::error("[HubspotApp:saveMergeInfo] ".$e->getMessage());
			return false;
		}
	}

	public function  getAssociations($objectId, $portalId, $definitionId) {
		$tokens = $this->getTokens($portalId);
		$hub_url = "https://api.hubapi.com/crm-associations/v1/associations/$objectId/HUBSPOT_DEFINED/".$definitionId;
		
		$associations = Func::request("GET", $hub_url, [
			'headers' => [
			'Content-Type' => 'application/json',
			"Authorization" => "Bearer $tokens[access_token]"]
		]);

		if(isset($associations->status)) {
			Log::error("[HubspotApp:getAssociations] error:".json_encode($associations));
		}
		return $associations->results ?? false; 
	}

	public function getDeals($portalId, $dealIds) {
		$deals = [];
		$tokens = $this->getTokens($portalId);
		foreach($dealIds as $dealId) {
			$hub_url = "https://api.hubapi.com/deals/v1/deal/".$dealId;
			$deal = Func::request("GET", $hub_url, [
				'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"]
			]);
			if(isset($deal->status)) {
				Log::error("[HubspotApp:getDeals] error:".json_encode($deal));
				continue;
			}
			$deals[] = $deal;
		}
		return $deals;
	}

	public function deleteContact($contact) {
		$objectId = $contact['vid'];
		$portalId = $contact['portal-id'];
		$dump = json_encode($contact);

		$tokens = $this->getTokens($portalId);
		$hub_url = "https://api.hubapi.com/contacts/v1/contact/vid/".$objectId;

		$res = Func::request("DELETE", $hub_url, [
			'headers' => [
			'Content-Type' => 'application/json',
			"Authorization" => "Bearer $tokens[access_token]"]
		]);

		if(isset($res->status)) {
			Log::error("[hubspotApp:deleteContact] error: ".json_encode($res));
		}
		return isset($res->vid) ? $this->saveDeleteInfo($objectId, $portalId, $dump) : false;
	}

	public function saveDeleteInfo($objectId, $portalId, $dump) {
		try {
			DdupStats::insert([
				'portal_id' => $portalId,
				'deleteVid' => $objectId,
				'deleteDump' => $dump,
				'type' => 'delete',
			]);
			return true;
		} catch(Exception $e) {
			Log::error("[HubspotApp:saveDeleteInfo] ".$e->getMessage());
			return false;
		}
	}

	public function getContactProperties($portalId) {
		$tokens = $this->getTokens($portalId);
		$hub_url = 'https://api.hubapi.com/properties/v1/contacts/properties';

		$res = Func::request('GET', $hub_url, [
			'headers' => [
			'Content-Type' => 'application/json',
			"Authorization" => "Bearer $tokens[access_token]"]
		]);

		if(isset($res->status)) {
			Log::error("[HubspotApp:getContactProperties] Exception: ".json_encode($res));
			return false;
		}
		return $res;
	}

	public function updateTimeline($portalId, $events, $eventUrl) {
		$tokens = $this->getTokens($portalId);
		$res = Func::request('PUT', $this->hsApp['url'].$eventUrl, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'json' => $events
		]);
		
		if(isset($res->status)) {
			Log::error("[HubspotApp:updateTimeline] error: ".json_encode($res));
			return false;
		}
		return true;
	}

	public function createContact($portal_id, $properties) {
		$tokens = $this->getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/contacts/v1/contact";
		$body = json_encode(utf8_converter($properties), JSON_UNESCAPED_UNICODE);

		$data = [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"
			],
			'body' => $body
		];

		$response =  Func::request('POST', $hub_url, $data);
		if(!isset($response->vid)) {
			Log::error("[HubspotApp]Error in createContact: ".json_encode($response));
			return false;
		}
		Log::info("[HubspotApp InboundContactCreate] for $portal_id\n".$body);
		return $response;
	}

	public function properties($portal_id) {
		$tokens = $this->getTokens($portal_id);
		$hub_url = "https://api.hubapi.com/properties/v1/contacts/groups/named/contactinformation?includeProperties=true";
		
		$response = Func::request('GET', $hub_url, [
			'headers' => [
				'Content-Type' => 'application/json',
				"Authorization" => "Bearer $tokens[access_token]"]
		]);

		if(!isset($response->properties)) {
			Log::error("[HubspotApp:properties] Error ".json_encode($response));
			return false;
		}
		return $response->properties;
	}
}
