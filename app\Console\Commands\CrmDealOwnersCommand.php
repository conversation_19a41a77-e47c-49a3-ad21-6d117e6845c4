<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmData, CrmContact, CrmCompany, CrmCompanyPrimaryReps, CrmEquipment, CrmOwners, CrmEquipmentAll};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmDealOwnersCommand extends Command
{

    protected $signature = "crm:dealowners";

    protected $description = "Update all hubspot deal owners";

    public function handle()
    {
        CrmEquipment::chunkById(200, function ($equipments) {
                foreach ($equipments as $items) {
                    $dealid = $items->hubspot_deal_id;
                    $equipmentId = $items->crm_equipment_id;
                    $deal = Hubspot::getDeal(722284, $dealid);
                    if(!$deal){
                        $this->error("deal with id $dealid could not fetch deal");
                        continue;
                    }
                    $companyId = $deal->properties->parent_id->value??"";
                    $deal_owner = "";
                    if (!empty($companyId)) {
                        $getCompanyPrimaryReps = Crm::getCompanyPrimaryReps($companyId);
                        if (!empty($getCompanyPrimaryReps)) {
                            foreach ($getCompanyPrimaryReps as $companyPrimaryReps) {
                                $owner_email = $companyPrimaryReps->email;
                                $role = $companyPrimaryReps->role;
                                if ($role == "Komatsu Rep") {
                                    $owner = CrmOwners::where('email', $owner_email)->first();
                                    if (isset($owner->owner_id)) {
                                        $deal_owner = $owner->owner_id;
                                    }
                                    break;
                                }
                            }
                        }
                    }
                    $props = Hubspot::buildDealProps([
                        'hubspot_owner_id' => $deal_owner
                    ]);
                    $response = Hubspot::updateDeal(722284, $dealid, $props);

                    if ($response === false) {
                        Log::error("[CrmDealOwnersCommand:handle] Deal updation failed: " . json_encode($response));
                        $this->error("deal with id $dealid update unsuccessful");
                        continue;
                    } else {
                        $equipment = CrmEquipmentAll::where('equipmentId', $equipmentId)->first();
                        if (isset($equipment->payload)) {
                            $payload = $equipment->payload;
                        }
                        try {
                            CrmEquipment::updateOrCreate(
                                [
                                    'hubspot_deal_id' => $response->dealId,
                                ],
                                [
                                    'hubspot_deal_id' => $response->dealId,
                                    'deal_owner_id' => $deal_owner,
                                    'payload' => $payload ?? null,
                                    'updated_at' => date('Y-m-d H:i:s'),
                                ]
                            );
                            $this->info("deal with id $dealid updated successfully");
                        } catch (Exception $e) {
                            Log::error("[CrmDealOwnersCommand:handle] Exception: " . $e->getMessage());
                            $this->error("deal with id $dealid db update unsuccessful");
                        }
                    }
                }
            }, $column = 'id');

        $this->info("success");
    }
}
