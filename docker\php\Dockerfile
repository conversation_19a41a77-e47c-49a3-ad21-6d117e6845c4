FROM php:7.4-fpm-alpine

# Update packages and install the required extensions
RUN apk update && apk upgrade && \ 
    apk add --no-cache libzip-dev libpng-dev libjpeg-turbo-dev freetype-dev && docker-php-ext-configure gd --with-freetype --with-jpeg && \
	docker-php-ext-install -j$(nproc) gd pdo pdo_mysql bcmath zip && \
    rm -rf /var/cache/apk/*

COPY ./docker/nginx/default.conf /etc/nginx/conf.d/default.conf
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

COPY ./docker/db/entrypoint.sh /etc/entrypoint.sh
RUN chmod +x /etc/entrypoint.sh

WORKDIR /var/www/html

COPY . .

RUN composer install

RUN chmod 777 -R bootstrap/
RUN chmod 777 -R storage/

ENTRYPOINT ["/etc/entrypoint.sh"]

# RUN composer install --no-dev --optimize-autoloader