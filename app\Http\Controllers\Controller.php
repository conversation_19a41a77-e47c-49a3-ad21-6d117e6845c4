<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function jsonOk($data = [], $code = 200)
    {
        $data = array_merge(['ok' => true], $data);

        return response()->json($data, $code);
    }

    public function jsonError($data = [], $code = 200)
    {
        $data = array_merge(['ok' => false], $data);

        return response()->json($data, $code);
    }
}
