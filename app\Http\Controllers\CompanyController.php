<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use Illuminate\Http\Request;
use App\Helpers\Hubspot;
use App\Models\{CrmCompanyFieldMapping,CrmCompanyField};

class CompanyController extends Controller
{

     public function getCompanyFields(Request $request)
    {
        try {
            // Get portal_id from request or use default
            $portal_id = $request->get('portal_id', *********); // Using the active portal_id

            //Get Company Property
            $response = Hubspot::getCompanyFields($portal_id);

            if ($response === false) {
                return $this->jsonError([
                    'error' => 'Failed to fetch company fields from HubSpot'
                ]);
            }
            return $this->jsonOk(['data' => $response]);
        } catch (Exception $e) {
            Log::error('[CompanyController:getCompanyFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching company fields',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getRmsFields(){
        $getRmsFields = CrmCompanyField::all();
        return $this->jsonOk(['data' => $getRmsFields]);
    }

    public function addCompanyFields(Request $request){
        try {
            $this->validate($request, [
                'portal_id' => 'required',
                'data' => 'required|array',
                'rms_field' => 'required',
            ]);

            $input = $request->input();
            //Get Company Property
            $response = Hubspot::addHubspotProperty($input['portal_id'],'companies',$input['data']);

            if ($response === false) {
                return $this->jsonError([
                    'error' => 'Failed to add company fields from HubSpot'
                ]);
            }
            
            // Save Company field Mapping
            CrmCompanyFieldMapping::updateOrInsert(
                [
                    'portal_id' => $input['portal_id'],
                    'hubspot_field' => $input['data']['name'],
                ],
                [
                    'rms_field' => $input['rms_field'],
                ]
            );

            return $this->jsonOk(['data' => json_decode($response, true)]);
        }   catch (Exception $e) {
            Log::error('[CompanyController:addCompanyFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while adding company fields',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function removeCompanyFields(Request $request){
        try {
            $this->validate($request, [
                'portal_id' => 'required',
                'name' => 'required',
            ]);

            $input = $request->input();
            //Get Company Property
            $response = Hubspot::deleteHubspotProperty($input['portal_id'],'companies',$input['name']);

            if (isset($response->status) && $response->status == 'error') {
                return $this->jsonError([
                    'error' => $response->message
                ]);
            }

            //Remove From Company Field Mapping Table

            CrmCompanyFieldMapping::where('portal_id', $input['portal_id'])
                ->where('hubspot_field', $input['name'])
                ->delete();

            return $this->jsonOk(['message' => 'Company field remove successfully.']);
        }   catch (Exception $e) {
            Log::error('[CompanyController:deleteCompanyFields] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while deleting company fields',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function saveCompanyFieldsMapping(Request $request){
        try {
            
            $this->validate($request, [
                'portal_id' => 'required',
                'mappings' => 'required|array',
            ]);

            $input = $request->input();

            $submittedHubspotFields = collect($input['mappings'])->pluck('hubspot_field')->toArray();

            // delete mappings that are not submitted
            
            CrmCompanyFieldMapping::where('portal_id', $input['portal_id'])
                ->whereNotIn('hubspot_field', $submittedHubspotFields)
                ->delete();

            //Save Company Field Mapping
            foreach ($input['mappings'] as $value) {
                CrmCompanyFieldMapping::updateOrCreate(
                    ['portal_id' => $input['portal_id'], 'hubspot_field' => $value['hubspot_field']],
                    ['rms_field' => $value['rms_field']]
                );
            }

            return $this->jsonOk(['message' => 'Company field mapping saved successfully.']);
        }   catch (Exception $e) {
            Log::error('[CompanyController:saveCompanyFieldsMapping] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while saving company field mapping',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getCompanyFieldMapping(Request $request){
        try {
            $input = $request->input();
            $getCompanyFieldMapping = CrmCompanyFieldMapping::where('portal_id', $input['portal_id'])->get();
            return $this->jsonOk(['data' => $getCompanyFieldMapping]);
        }   catch (Exception $e) {
            Log::error('[CompanyController:getCompanyFieldMapping] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching company field mapping',
                'message' => $e->getMessage()
            ]);
        }
    }

}

