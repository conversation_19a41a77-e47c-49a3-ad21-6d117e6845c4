<template>
  <LoginLayout>
  <main class="max-w-7xl mx-auto px-6 py-12">
    <!-- Header Section -->
    <div class="flex flex-col-reverse md:flex-row items-center gap-8">
      <!-- Text Content -->
      <div class="md:w-1/2">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">RMS Integration</h2>
        <p class="text-gray-700 mb-2">
          Improve your productivity <strong class="font-semibold">Mapping field directly in to your hubspot</strong> by RMS.
        </p>
        <p class="text-gray-700">
          Simply authorize your HubSpot account and voila! RMS gets started with mapping your fields, for deals, contacts and company starting from the oldest to newest. Also, you can handle the users through the system.
        </p>
      </div>

      <!-- Image -->
      <div class="md:w-1/2 flex justify-center">
        <img :src="getAssetUrl('images/error.png')" alt="Integration Illustration" class="max-w-xs md:max-w-sm w-full" />
      </div>
    </div>

    <!-- Authorization Header -->
    <div class="mt-12">
      <div class="flex items-center bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-6 py-4 rounded-md shadow">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3" />
        </svg>
        <span class="text-lg font-semibold">Authorization</span>
      </div>

      <!-- Authorization Info Box -->
      <div class="bg-white mt-4 p-6 rounded-md border border-gray-200 shadow-sm">
        <p class="text-gray-700 mb-4">
          Please authorize LitePics to access your website in order to generate a personalized offer for you.
        </p>
        <a
          @click="authorize"
          target="_blank"
          class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium px-5 py-2 rounded transition"
        >
          Authorize Now
        </a>
      </div>
    </div>
  </main>
  </LoginLayout>
</template>

<script setup>
import { ref } from 'vue';
import LoginLayout from '../layouts/LoginLayout.vue';
import { useAssets } from '../composables/useAssets';

const { getAssetUrl } = useAssets();

const authorize = () => {
  window.location.href = `${window.APP_URL}/hubauth`;
}
</script>
