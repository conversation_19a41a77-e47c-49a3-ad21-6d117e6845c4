<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hu<PERSON>pot};
use App\Models\{CrmPortals, CrmData, CrmContact, CrmCompanyPrimaryReps, CrmOwners, ExcludedContactId,CrmContactFieldMapping};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmContactsCommand extends Command
{

    protected $signature = "crm:contacts";

    protected $description = "Get Crm data for contacts";

    public function handle()
    {
        // $checkFlag = CrmData::select('status')
        //     ->where('type', '=', 'contact')
        //     ->where('from', '=', 'crm')
        //     ->where('status', '=', '1')
        //     ->whereDate('created_at', today())->get();

        // if (!isset($checkFlag[0]->status)) {
            $data = [];
            $portals = CrmPortals::get();
            $contactMappingFields = [];
            //Get Contact Mapfields
            

            foreach ($portals as $portal) {

                $contactMapFields = CrmContactFieldMapping::where('portal_id', $portal->portal_id)->get();

                if($contactMapFields){
                    foreach($contactMapFields as $contactMapField){
                        $contactMappingFields[$contactMapField->hubspot_field] = $contactMapField->rms_field;
                    }
                }

                $data['portalId'] = $portal->portal_id;
                $data['fromDate'] = '2025-01-01'; //date("Y-m-d", time() - 86400);
                $data['contactId'] = [];

                $resData = Crm::getContacts($data);
                if (isset($resData) && !empty($resData)) {
                    foreach ($resData as $res) {
                        if (isset($res->data->items) && !empty($res->data->items)) {
                            foreach ($res->data->items as $items) {
                                if (!empty($items->email)) {
                                    $exludedEmail = ExcludedContactId::where('email', $items->email)->first();
                                    if ($exludedEmail) {
                                        continue;
                                        //skip if email is in ExcludedContact
                                    }
                                }

                                $departments = ['Accounting'];
                                if (in_array($items->department, $departments)) {
                                    continue;
                                }
                                $companyId = $items->companyId ?? "";
                                $contactId = $items->contactId ?? "";
                                // $get_owner = CrmCompanyPrimaryReps::select('hubspot_owner_id')
                                //                                 ->where('company_id', $companyId)->first();
                                // $contact_owner = $get_owner['hubspot_owner_id'] ?? "";
                                $contact_owner = "";
                                if (!empty($companyId)) {
                                    $getCompanyPrimaryReps = Crm::getCompanyPrimaryReps($companyId,$portal->portal_id);
                                    if (!empty($getCompanyPrimaryReps)) {
                                        foreach ($getCompanyPrimaryReps as $companyPrimaryReps) {
                                            $owner_email = $companyPrimaryReps->email;
                                            $role = $companyPrimaryReps->role;
                                            if ($role == "Komatsu Rep") {
                                                $owner = CrmOwners::where('email', $owner_email)->first();
                                                if (isset($owner->owner_id)) {
                                                    $contact_owner = $owner->owner_id;
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                                // $props = Hubspot::buildContactProps([
                                //     'crmseries_company_id' => $items->companyId ?? "",
                                //     'firstname' => $items->firstName ?? "",
                                //     'middle_name' => $items->middleName ?? "",
                                //     'lastname' => $items->lastName ?? "",
                                //     'nickname' => $items->nickName ?? "",
                                //     'phone' =>  $items->phone ?? "",
                                //     'cell' => $items->cell ?? "",
                                //     'fax' => $items->fax ?? "",
                                //     'email' => $items->email ?? "",
                                //     'title' => $items->title ?? "",
                                //     'position' => $items->position ?? "",
                                //     'department' => $items->department ?? "",
                                //     'contact_id' => $items->contactId ?? "",
                                //     // 'entered' => $items['entered'] ?? "",
                                //     'lastmodified' => $items->lastModified ?? "",
                                //     'hubspot_id' => $items->hubspotId ?? "",
                                //     'hubspot_owner_id' => $contact_owner
                                // ]);

                                // $props = Hubspot::buildContactProps($contactMappingFields);
                                $props = Hubspot::buildContactPropsFromMapping($contactMappingFields, $items);
                                if ($items->email !== "" && (strtolower($items->firstName) !== 'primary')) {
                                    $email = $items->email;
                                    $response = Hubspot::createOrUpdateContact(
                                        $portal->portal_id,
                                        $props,
                                        $email
                                    );
                                    if ($response === false) {
                                        Log::error("[CrmContactsCommand:handle] Contact $contactId creation/updation failed");
                                        // return false;
                                    } else {
                                        try {
                                            CrmContact::updateOrCreate(
                                                [
                                                    'crm_contact_id' => $contactId,
                                                    'hubspot_contact_id' => $response,
                                                ],
                                                [
                                                    'crm_contact_id' => $contactId,
                                                    'hubspot_contact_id' => $response,
                                                    'crm_company_id' => $companyId,
                                                    'contact_owner_id' => $contact_owner,
                                                    'created_at' => $data['fromDate'],
                                                    'updated_at' => date('Y-m-d H:i:s'),
                                                ]
                                            );
                                        } catch (Exception $e) {
                                            Log::error("[CrmContactsCommand:handle] Exception: " . $e->getMessage());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    Log::info("[CrmContactsCommand:handle] data for " . $data['fromDate'] . " : " . json_encode($res));
                    try {
                        $status = 0;
                        if (isset($res->data->items) && !empty($res->data->items)) {
                            $status = 1;
                        }
                        CrmData::updateOrCreate(
                            [
                                'created_at' => $data['fromDate'],
                                'type' => 'contact',
                            ],
                            [
                                'portal_id' => $portal->portal_id,
                                'type' => 'contact',
                                'from' => 'crm',
                                'data' => json_encode($res->data),
                                'created_at' => $data['fromDate'],
                                'status' => $status
                            ]
                        );
                    } catch (Exception $e) {
                        Log::error("[CrmContactsCommand:handle] Exception: " . $e->getMessage());
                    }
                } else {
                    Log::info("[CrmContactsCommand:handle] No data for " . $data['fromDate'] . " : " . json_encode($resData));
                }
            }
        // }
    }
}
