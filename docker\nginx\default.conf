server {
	listen 80;
	index index.php;
	server_name localhost;
	root /var/www/html/public;
	error_log /var/log/nginx/error.log;
	access_log /var/log/nginx/access.log;
	proxy_buffering off;

	location / {
		try_files $uri /index.php$is_args$args;
	}

	location ~ ^/index\\.php(/|$) {
		fastcgi_pass php:9000;
		fastcgi_split_path_info ^(.+\\.php)(/.*)$;
		include fastcgi_params;

		fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
		fastcgi_param DOCUMENT_ROOT $realpath_root;

		fastcgi_buffer_size 128k;
		fastcgi_buffers 4 256k;
		fastcgi_busy_buffers_size 256k;

		internal;
	}

	location ~ \\.php$ {
		return 404;
	}
}