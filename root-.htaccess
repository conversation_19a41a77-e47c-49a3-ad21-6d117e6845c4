# Root .htaccess file
# This should be placed in the parent directory of your Laravel project
# For example, if your project is in /var/www/html/rmscrmseries/
# This file should be in /var/www/html/.htaccess

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle rmscrmseries subdirectory
    RewriteCond %{REQUEST_URI} ^/rmscrmseries/(.*)$
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^rmscrmseries/(.*)$ /rmscrmseries/public/index.php [L,QSA]
    
    # Direct access to public assets
    RewriteCond %{REQUEST_URI} ^/rmscrmseries/(.*)$
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^rmscrmseries/(.*)$ /rmscrmseries/public/$1 [L]
</IfModule>
