<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hu<PERSON>pot};
use App\Models\{CrmPortals, CrmData, CrmContact, CrmCompany, CrmCompanyPrimaryReps, CrmEquipment, CrmOwners, CrmEquipmentAll,CrmDealFieldMapping};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmDealsCommand extends Command
{

    protected $signature = "crm:deals";

    protected $description = "Update all hubspot deal properties";

    public function handle()
    {
        $dealMappingFields = [];
        //Get Deal Mapfields
        $dealMapFields = CrmDealFieldMapping::all();

        if($dealMapFields){
            foreach($dealMapFields as $dealMapField){
                $dealMappingFields[$dealMapField->hubspot_field] = $dealMapField->rms_field;
            }
        }

        $portals = CrmPortals::first();
        $portal_id = '';
        if($portals){
            $portal_id = $portals->portal_id;
        }

        CrmEquipmentAll::chunkById(200, function ($equipments) use ($dealMappingFields,$portal_id) {
            foreach ($equipments as $items) {
                $items = $items->payload;
                $items = json_decode($items);
                if (isset($items->make) && $items->make != "Komatsu") {
                    continue;
                }

                //EquipmentYear - Make - Model - SerialNumber - CompanyName
                $name = (isset($items->parentName) || isset($items->make) || isset($items->model) || isset($items->serialNumber) || isset($items->equipmentYear))
                    ? ($items->equipmentYear ?? "") . "" . ' - ' . ($items->make ?? "") . "" . ' - ' . ($items->model ?? "") . "" . ' - ' . ($items->serialNumber ?? "") . "" . ' - ' . ($items->parentName ?? "")
                    : "-";
                $startDate = ($items->startDate !== NULL) ? strtotime($items->startDate) * 1000 : "";
                $lastSmrDate = ($items->lastSmrDate !== NULL) ? strtotime($items->lastSmrDate) * 1000 : "";
                $stdWarrantyEndDate = "";
                $extWarrantyEndDate = "";
                $stdWarrantyHours = "";
                $extWarrantyHours = "";
                $leaseExpire = "";
                $tier = "";
                $warrantyTypeArr = [];
                $wType = "";
                $equipmentId = $items->equipmentId ?? "";
                $companyId = $items->parentId ?? "";
                $hubspotCompanyId = "";
                $hubspotContactId = "";
                $deal_owner = "";
                if (!empty($companyId)) {
                    $getCompanyPrimaryReps = Crm::getCompanyPrimaryReps($companyId,$portal_id);
                    
                    if (!empty($getCompanyPrimaryReps)) {
                        foreach ($getCompanyPrimaryReps as $companyPrimaryReps) {
                            $owner_email = $companyPrimaryReps->email;
                            $role = $companyPrimaryReps->role;
                            if ($role == "Komatsu Rep") {
                                $owner = CrmOwners::where('email', $owner_email)->first();
                                if (isset($owner->owner_id)) {
                                    $deal_owner = $owner->owner_id;
                                }
                                break;
                            }
                        }
                    }
                }
                $deal_owner = '78856535';
                Log::info('owner id: '.$deal_owner);
                foreach ($items->warrenties as $warranties) {
                    if ($warranties->warrantyType == 'ST') {
                        $stdWarrantyEndDate = ($warranties->endDate !== NULL) ? strtotime($warranties->endDate) * 1000 : "";
                        $stdWarrantyHours = ($warranties->hours !== 0) ? $warranties->hours : 0;
                    } else {
                        $extWarrantyEndDate = ($warranties->endDate !== NULL) ? strtotime($warranties->endDate) * 1000 : "";
                        $extWarrantyHours = ($warranties->hours !== 0) ? $warranties->hours : 0;
                    }
                    array_push($warrantyTypeArr, $warranties->warrantyType);
                }
                foreach ($items->udfs as $udfs) {
                    if ($udfs->fieldName == 'LeaseExpire') {
                        $leaseExpire = ($udfs->fieldValues[0] !== NULL) ? strtotime($udfs->fieldValues[0]) * 1000 : "";
                    }
                    if ($udfs->fieldName == 'Tier') {
                        $tier = ($udfs->fieldValues[0] !== NULL) ? $udfs->fieldValues[0] : "";
                    }
                }
                $wType = (count($warrantyTypeArr) > 1) ? implode(";", $warrantyTypeArr) : implode("", $warrantyTypeArr);
                $staticArray = array(
                    'dealname' => $name,
                    'hubspot_owner_id' => $deal_owner
                );
                //    $props = Hubspot::buildDealProps($dealMappingFields);
                   $props = Hubspot::buildDealPropsFromMapping($dealMappingFields, $items,$staticArray);
                // $props = Hubspot::buildDealProps([
                //     'equipment_id' => $items->equipmentId ?? "",
                //     'parent_name' => $items->parentName ?? "",
                //     'hubspot_id' => $items->hubspotId ?? "",
                //     'description' => $items->description ?? "",
                //     'start_date___date_picker' => $startDate ?? "",
                //     'start_hours___numeric' => $items->startHours ?? "",
                //     'lastsmrdate___date_picker' => $lastSmrDate ?? "",
                //     'lastsmr_date___numeric' => $items->lastSmr ?? "",
                //     'latitude' => $items->latitude ?? "",
                //     'longitude' => $items->longitude ?? "",
                //     'hours_per_day' => $items->hoursPerDay ?? "",
                //     'hours_per_day_range' => $items->hoursPerDayRange ?? "",
                //     'inventory' => $items->inventory ?? "",
                //     'fleet' => $items->fleet ?? "",
                //     'fleet_type' => $items->fleetType ?? "",
                //     'status' => $items->status ?? "",
                //     'new_used' => $items->newUsed ?? "",
                //     'cbgrp' => $items->groupCode ?? "",
                //     'hours_per_day_override' => $items->hoursPerDayOverride ?? "",
                //     'uc_target_wear_percent' => $items->ucTargetWearPercent ?? "",
                //     'exclude_uc_tracking' => $items->excludeUCTracking ?? "",
                //     'complete' => $items->complete ?? "",
                //     'satellite_identifier' => $items->satelliteIdentifier ?? "",
                //     'parent_id' => $items->parentId ?? "",
                //     'parent_type' => $items->parentType ?? "",
                //     'cbmod' => $items->model ?? "",
                //     'cbmyr' => $items->equipmentYear ?? "",
                //     'cbmak' => $items->make ?? "",
                //     'serial_no_' => $items->serialNumber ?? "",
                //     'cbord' => $items->stockNumber ?? "",
                //     'equipment_number' => $items->equipmentNumber ?? "",
                //     'category_id' => $items->categoryId ?? "",
                //     'stdwtdenddate' => $stdWarrantyEndDate,
                //     'extwarrantyenddate' => $extWarrantyEndDate,
                //     'warranty_type___multi_select' => $wType ?? "",
                //     'basicwarrantyhrs' => $stdWarrantyHours,
                //     'extwarrantyhrs' => $extWarrantyHours,
                //     'lease_expiry_date___date_picker' => $leaseExpire ?? "",
                //     'tier' => $tier,
                //     'dealname' => $name,
                //     'hubspot_owner_id' => $deal_owner
                // ]);

                $checkequipment = CrmEquipment::where('crm_equipment_id', $equipmentId)->first();
                if (isset($checkequipment->hubspot_deal_id)
                && Hubspot::dealExists($portal_id, $checkequipment->hubspot_deal_id)) {
                    $dealid = $checkequipment->hubspot_deal_id;
                    $response = Hubspot::updateDeal($portal_id, $dealid, $props);
                } else {
                    $response = Hubspot::createDeal($portal_id, $props);
                    //continue;
                }

                if ($response === false) {
                    Log::error("[CrmDealsCommand:handle] Deal updation failed: " . json_encode($items));
                    $this->error("deal with equipment id $equipmentId update unsuccessful");
                    continue;
                } else {
                    try {
                        CrmEquipment::updateOrCreate(
                            [
                                'crm_equipment_id' => $equipmentId,
                                'hubspot_deal_id' => $response->dealId,
                            ],
                            [
                                'crm_equipment_id' => $equipmentId,
                                'hubspot_deal_id' => $response->dealId,
                                'deal_owner_id' => $deal_owner,
                                'payload' => json_encode($items),
                                'updated_at' => date('Y-m-d H:i:s'),
                            ]
                        );
                        $this->info("deal with equipment id $equipmentId updated successfully");
                    } catch (Exception $e) {
                        Log::error("[CrmDealsCommand:handle] Exception: " . $e->getMessage());
                        $this->error("deal with equipment id $equipmentId db update unsuccessful");
                    }
                }
            }
        }, $column = 'id');

        $this->info("deals updated successfully");
    }
}
