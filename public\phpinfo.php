<?php
// Simple PHP test file
echo "<h1>PHP is working!</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Server: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Script Name: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";

// Check if Laravel vendor directory exists
$vendorPath = __DIR__ . '/../vendor/autoload.php';
if (file_exists($vendorPath)) {
    echo "<p style='color: green;'>✅ Laravel vendor directory found</p>";
} else {
    echo "<p style='color: red;'>❌ Laravel vendor directory not found at: " . $vendorPath . "</p>";
}

// Check if .env file exists
$envPath = __DIR__ . '/../.env';
if (file_exists($envPath)) {
    echo "<p style='color: green;'>✅ .env file found</p>";
} else {
    echo "<p style='color: red;'>❌ .env file not found at: " . $envPath . "</p>";
}

// Test Laravel bootstrap
try {
    require_once $vendorPath;
    echo "<p style='color: green;'>✅ Laravel autoloader loaded successfully</p>";
    
    // Try to load Laravel app
    $app = require_once __DIR__ . '/../bootstrap/app.php';
    echo "<p style='color: green;'>✅ Laravel application bootstrapped successfully</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Laravel bootstrap failed: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li><a href='index.php'>Test Laravel Application</a></li>";
echo "<li><a href='test.html'>Back to Test Page</a></li>";
echo "</ul>";
?>
