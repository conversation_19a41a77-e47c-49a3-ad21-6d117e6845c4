<template>
  <div v-if="show" class="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl p-6 w-full max-w-md shadow-lg relative">
      <!-- Close Button -->
      <button @click="close" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-xl">
        &times;
      </button>

      <!-- Title -->
      <h2 class="text-xl font-semibold text-gray-900 mb-6">Add HubSpot field</h2>

      <!-- HubSpot Field Input -->
      <div class="mb-4">
        <label class="block text-sm text-gray-700 mb-1">HubSpot fields</label>
        <input
          type="text"
          v-model="hubspotField"
          placeholder="Field of study"
          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <!-- Hubspot Field type Dropdown -->
      <div class="mb-6">
        <label class="block text-sm text-gray-700 mb-1">Hubspot field Type</label>
        <select
          v-model="hubspotFieldType"
          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option disabled value="">Select field type</option>
          <option v-for="field in hubspotOptions" :key="field" :value="field">{{ field.label }}</option>
        </select>
      </div>

      <!-- RMS Field Dropdown -->
      <div class="mb-6">
        <label class="block text-sm text-gray-700 mb-1">RMS fields</label>
        <select
          v-model="rmsField"
          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option disabled value="">Select field</option>
          <option v-for="field in rmsOptions" :key="field.id" :value="field">{{ field.field_name }} ({{ field.field_type || field.type }})</option>
        </select>
      </div>

      <!-- Field Type Validation Message -->
      <div v-if="rmsField && hubspotFieldType" class="mb-4 p-3 rounded-lg">
        <div v-if="fieldTypesMatch" class="text-green-800 bg-green-100 border border-green-200 rounded-lg p-3">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Field types match!</span>
          </div>
          <p class="text-sm mt-1">RMS: {{ rmsField.field_type || rmsField.type }} ↔ HubSpot: {{ hubspotFieldType.type }}</p>
        </div>
        <div v-else class="text-red-800 bg-red-100 border border-red-200 rounded-lg p-3">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            <span class="font-medium">Field types don't match!</span>
          </div>
          <p class="text-sm mt-1">RMS: {{ rmsField.field_type || rmsField.type }} ≠ HubSpot: {{ hubspotFieldType.type }}</p>
          <p class="text-sm mt-1">Please select fields with matching types.</p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-3">
        <button @click="close" class="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100">
          Cancel
        </button>
        <button
          @click="addField"
          :disabled="!fieldTypesMatch || !hubspotField || !rmsField || !hubspotFieldType"
          class="px-4 py-2 rounded-lg text-white transition-colors duration-150"
          :class="{
            'bg-blue-600 hover:bg-blue-700 cursor-pointer': fieldTypesMatch && hubspotField && rmsField && hubspotFieldType,
            'bg-gray-400 cursor-not-allowed': !fieldTypesMatch || !hubspotField || !rmsField || !hubspotFieldType
          }"
        >
          Add
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed, watch } from 'vue'

const props = defineProps({
  show: Boolean,
  rmsOptions: {
    type: Array,
    default: () => []
  },
  hubspotOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'add'])

const hubspotField = ref('')
const rmsField = ref('')
const hubspotFieldType = ref('')

// Reset form fields when modal is opened
watch(() => props.show, (newValue) => {
  if (newValue) {
    // Reset all form fields when modal opens
    hubspotField.value = ''
    rmsField.value = ''
    hubspotFieldType.value = ''
  }
})

// Computed property to check if field types match
const fieldTypesMatch = computed(() => {
  if (!rmsField.value || !hubspotFieldType.value) return false;

  const rmsFieldType = rmsField.value.field_type;
  const hubspotType = hubspotFieldType.value.type;

  // Handle type mappings between RMS and HubSpot
  if (hubspotType === 'number') {
    // HubSpot 'number' matches RMS 'integer', 'double', 'float'
    return ['integer', 'double', 'float', 'number'].includes(rmsFieldType);
  }

  if (hubspotType === 'bool') {
    // HubSpot 'bool' matches RMS 'boolean'
    return ['boolean', 'bool'].includes(rmsFieldType);
  }

  if (hubspotType === 'date' || hubspotType === 'datetime') {
    // HubSpot 'date'/'datetime' matches RMS 'date', 'datetime'
    return ['date', 'datetime'].includes(rmsFieldType);
  }

  if (hubspotType === 'string') {
    // HubSpot 'string' matches RMS 'string', 'text'
    return ['string', 'text'].includes(rmsFieldType);
  }

  // Default: exact match for other types
  return rmsFieldType === hubspotType;
});

const resetForm = () => {
  hubspotField.value = ''
  rmsField.value = ''
  hubspotFieldType.value = ''
}

const close = () => {
  resetForm()
  emit('close')
}

const addField = () => {
  if (hubspotField.value && rmsField.value && hubspotFieldType.value) {
    // Use the computed property for validation instead of duplicating logic
    if (!fieldTypesMatch.value) {
      // Field type mismatch is already shown in the UI, so we don't need to show another alert
      return;
    }

    emit('add', { hubspotField: hubspotField.value, rmsField: rmsField.value, hubspotFieldType: hubspotFieldType.value })
    close()
  } else {
    // Required fields validation is handled by the disabled button state
    return;
  }
}
</script>
