<?php 

namespace App\Helpers;

Class Options {
	
	public $hsApp;
	public $hsConfig;

	public $mode = [
		'hsApp' => 'hubchat'
	];

	public $domains = [
		'hubchat' => 'https://niswey.net/',
		'hubchat-staging' => 'https://niswey.net/'
	];

	public $integrationApps = [
		'hubchat' => [
			"grant_type" => "authorization_code",
			"client_id" => "1ed514bd-9943-4c88-bfc0-7e12f8843a28",
			"client_secret" => "6d760d26-ae83-479d-8ca6-75b4d0193ba0",
			"redirect_uri" => "https://niswey.net/hubspot",
			"code" => 0
		],

		'hubchat-staging' => [
			"grant_type" => "authorization_code",
			"client_id" => "eef4d0c5-f9e9-43cc-8d27-96b1b4e3ea7a",
			"client_secret" => "33334542-2aad-49c5-a940-3fdc46ae2337",
			"redirect_uri" => "https://niswey.net/hubspot",
			"code" => 0
		]
	];

	public $integrationConfig = [
		'hubchat' => [
			'url' => "https://api.hubapi.com/integrations/v1/189698/",
			'app_id' => 189698,
			'user_id' => 6289081,
			'user_event' => 387214,
			'hubspot_event' => 387215,
			'bearer' => null
		],

		'hubchat-staging' => [
			'url' => "https://api.hubapi.com/integrations/v1/200252/",
			'app_id' => 200252,
			'user_id' => 6289081,
			'user_event' => 392829,
			'hubspot_event' => 392830,
			'bearer' => null
		]
	];

	function __construct() {
		$this->hsApp = $this->integrationApps[$this->mode['hsApp']];
		$this->hsConfig = $this->integrationConfig[$this->mode['hsApp']];
	}

	function hsApp() {
		return $this->hsApp;
	}

	function hsConfig() {
		return $this->hsConfig;
	}

	function jsDomain() {
		// return $this->domains[$this->mode['hsApp']];
		return env("APP_URL");
	}

}
