const mix = require('laravel-mix');
const webpack = require('webpack');

// Configure public path for subdirectory deployment
mix.options({
    resourceRoot: process.env.VUE_PUBLIC_PATH || '/',
});

// Define environment variables for Vue
mix.webpackConfig({
    plugins: [
        new webpack.DefinePlugin({
            'process.env.VUE_ROUTER_BASE': JSON.stringify(process.env.VUE_ROUTER_BASE || '/'),
            'process.env.VUE_APP_BASE_URL': JSON.stringify(process.env.VUE_APP_BASE_URL || ''),
            'process.env.MIX_APP_URL': JSON.stringify(process.env.VUE_APP_BASE_URL || process.env.APP_URL || ''),
        }),
    ]
});

mix.js('resources/js/app.js', 'public/js')
    .vue()
    .postCss('resources/css/app.css', 'public/css', [
        require('tailwindcss'),
        require('autoprefixer'),
    ])
    .version();

// Set public path for production
if (mix.inProduction()) {
    mix.setPublicPath('public');
}
