<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmData, CrmContact, CrmCompanyPrimaryReps, CrmOwners};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmOwnersCommand extends Command
{

    protected $signature = "crm:owners";

    protected $description = "Get Crm data for owners";

    public function handle()
    {
        $response = Hubspot::fetchOwners();
        foreach ($response as $owner) {
            CrmOwners::updateOrCreate(['owner_id' => $owner->ownerId], ['email' => $owner->email]);
        }
        Log::info("[CrmOwnersCommand:handle] command successfull for date : " . today());
    }
}
