<?php 

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\Crm;
use App\Models\{CrmPortals, CrmData};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmMarketingCommand extends Command {

	protected $signature = "crm:marketing";

	protected $description = "Get Crm data for marketing";  

	public function handle() {
        $checkFlag = CrmData::select('status')
                            ->where('type', '=', 'marketing')
                            ->where('from', '=', 'crm')
                            ->where('status', '=', '1')
                            ->whereDate('created_at', today())->get();
        if(!isset($checkFlag[0]->status)){
            $data = [];
            $portals = CrmPortals::get();
            foreach ($portals as $portal) {
                $data['recordType'] = "";
                $data['recordId'] = 0;
                $data['externalId'] = "";
                $data['externalRecordType'] = "";
                $data['externalProvider'] = "";
                $data['portalId'] = $portal->portal_id;
                $res = Crm::getMarketing($data);
                if(isset($res) && (isset($res->hasErrors)) && ($res->hasErrors == false)){
                    try {
                        $status = 0;
                        if(isset($res->data) && !empty($res->data)){ $status = 1; }
                        CrmData::updateOrCreate(
                            [
                                'created_at' => date('Y-m-d'),
                                'type' => 'marketing',
                            ], 
                            [
                                'portal_id' => $portal->portal_id, 
                                'type' => 'marketing',
                                'from' => 'crm',
                                'data' => json_encode($res->data),
                                'created_at' => date('Y-m-d'),
                                'status' => $status
                            ]
                        );
                    } catch (Exception $e) {
                        Log::error("[CrmMarketingCommand:handle] Exception: ".$e->getMessage());
                    }
                }else{
                    Log::error("[CrmMarketingCommand:handle] No data: ".$res);
                }
            }
        }
	}
}
