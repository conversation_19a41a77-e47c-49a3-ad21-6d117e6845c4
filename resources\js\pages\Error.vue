<template>
    <DefaultLayout>
        <div class="flex items-center justify-center min-h-screen bg-white px-4">
    <div class="border border-dotted border-blue-300 rounded-lg p-10 max-w-md text-center shadow-sm">
      <!-- Image -->
      <img
        :src="getAssetUrl('images/error.png')"
        alt="Disconnected"
        class="mx-auto mb-6 w-40 h-40"
      />

      <!-- Heading -->
      <h2 class="text-lg font-semibold text-gray-900 mb-2">
        {{ errorTitle }}
      </h2>

      <!-- Description -->
      <p class="text-sm text-gray-600 mb-6">
        {{ errorMessage }}
      </p>

      <!-- Reload Button -->
      <button
        @click="reloadPage"
        class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium px-4 py-2 rounded"
      >
        Click to reload this page
      </button>
    </div>
  </div>
    </DefaultLayout>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import DefaultLayout from '../layouts/DefaultLayout.vue';
import { useAssets } from '../composables/useAssets';

const { getAssetUrl } = useAssets();

const errorTitle = ref('Apologies, it seems there was an interruption')
const errorMessage = ref('This might occur due to a service disruption or an issue with your internet connection.')

onMounted(() => {
  // Check for custom error message in URL parameters
  const urlParams = new URLSearchParams(window.location.search)
  const customMessage = urlParams.get('message')

  if (customMessage) {
    errorTitle.value = 'Access Denied'
    errorMessage.value = decodeURIComponent(customMessage)
  }
})

const reloadPage = () => {
  window.location.reload()
}
</script>
