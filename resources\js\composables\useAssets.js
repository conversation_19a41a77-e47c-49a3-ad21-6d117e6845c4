export function useAssets() {
  
  const getAssetUrl = (path) => {
    const baseUrl = window.APP_URL || '';
    const publicBaseUrl = window.APP_PUBLIC_PATH  || '';
    console.log('baseURL',baseUrl);
    console.log('publicBaseUrl',publicBaseUrl);
    console.log('finalPath',`${publicBaseUrl}/${path}`);
    // Remove leading slash from path if present to avoid double slashes
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    return `${publicBaseUrl}/${cleanPath}`;
  };

  return {
    getAssetUrl
  };
}
