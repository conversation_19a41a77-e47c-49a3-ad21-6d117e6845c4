<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\DealController;
use App\Http\Controllers\UserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

//Users

Route::get('/hubspot/users', [UserController::class, 'getUsers']);
Route::get('/user', [UserController::class, 'getUsersDetail']);
Route::post('/hubspot/users/save', [UserController::class, 'saveUsers']);


//Company

Route::get('/hubspot/company-fields', [CompanyController::class, 'getCompanyFields']);
Route::get('/company/rms-fields', [CompanyController::class, 'getRmsFields']);
Route::post('/hubspot/add-company-fields', [CompanyController::class, 'addCompanyFields']);
Route::post('/hubspot/remove-company-fields', [CompanyController::class, 'removeCompanyFields']);
Route::post('/company/fields-mapping', [CompanyController::class, 'saveCompanyFieldsMapping']);
Route::get('/company/field-mapping', [CompanyController::class, 'getCompanyFieldMapping']);

//Contact

Route::get('/hubspot/contact-fields', [ContactController::class, 'getContactFields']);
Route::get('/contact/rms-fields', [ContactController::class, 'getRmsFields']);
Route::post('/hubspot/add-contact-fields', [ContactController::class, 'addContactFields']);
Route::post('/hubspot/remove-contact-fields', [ContactController::class, 'removeContactFields']);
Route::post('/contact/fields-mapping', [ContactController::class, 'saveContactFieldsMapping']);
Route::get('/contact/field-mapping', [ContactController::class, 'getContactFieldMapping']);

//Deal

Route::get('/hubspot/deal-fields', [DealController::class, 'getDealFields']);
Route::get('/deal/rms-fields', [DealController::class, 'getRmsFields']);
Route::post('/hubspot/add-deal-fields', [DealController::class, 'addDealFields']);
Route::post('/hubspot/remove-deal-fields', [DealController::class, 'removeDealFields']);
Route::post('/deal/fields-mapping', [DealController::class, 'saveDealFieldsMapping']);
Route::get('/deal/field-mapping', [DealController::class, 'getDealFieldMapping']);