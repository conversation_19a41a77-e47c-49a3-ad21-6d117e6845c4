<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmDealField};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmDealFieldCommand extends Command
{

    protected $signature = "crm:deals-field";

    protected $description = "Get RMS fields for contacts";

     public function handle()
    {
            $data = [];
            $portals = CrmPortals::get();

            foreach ($portals as $portal) {
                $data['EquipmentIds'] = [];
                $data['portalId'] = $portal->portal_id;
                $data['fromDate'] = '2019-01-01'; //date("Y-m-d", time() - 86400);
                Log::info("[CrmEquipmentsCommand:handle] command started for date : " . $data['fromDate']);
                $resData = Crm::getEquipments($data,true);
                if (isset($resData) && !empty($resData)) {
                        if (isset($resData->data->items) && !empty($resData->data->items)) {

                            $firstItem = $resData->data->items[0];

                            $fieldKeys = array_keys((array)$firstItem);
                            foreach ($fieldKeys as $fieldName) {
                                 $value = $firstItem->$fieldName ?? null;
                                $fieldType = gettype($value);

                                CrmDealField::updateOrInsert(
                                    ['field_name' => $fieldName],
                                    [
                                        'field_name' => $fieldName,
                                        'field_type' => $fieldType,
                                        'created_at' => date('Y-m-d H:i:s'),
                                        'updated_at' => date('Y-m-d H:i:s')
                                    ]
                                );

                                Log::info("[CrmDealFieldCommand:handle] Insert field for " . $fieldName);
                            }
                            return;
                        }
                } else {
                    Log::info("[CrmDealFieldCommand:handle] No data for " . $data['fromDate']);
                }
            }
    }
}
