<?php 

namespace App\Traits;

use App\Models\Users;
use App\Models\Portals;
use App\Helpers\ChatApi;
use App\Models\WhatsApp;
use App\Models\Dialogs;
use App\Models\Instances;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

trait HelperTrait {

	public function noAccessResponse($hsApp, $email) {
		$defaultResponse = [
			"results" => [
				[
					"objectId" => $hsApp['config']['objectId'],
					"title" => "Access Denied",
					"properties" => [
			        	[
			        		"label" => "Access for the app is not allowed for the user",
					        "dataType" => "STRING",
					        "value" => $email
			        	]
			        ]
				]
			]
		];
		return $defaultResponse;
	}

	public function notPaidResponse($hsApp) {
		$defaultResponse = [
			"results" => [
				[
					"objectId" => $hsApp['config']['objectId'],
					"title" => "Portal not Activated",
					"properties" => [
			        	[
			        		"label" => "Contact support to activate your Account",
					        "dataType" => "STRING",
					        "value" => '<EMAIL>'
			        	]
			        ]
				]
			]
		];
		return $defaultResponse;
	}

	// resets DB when user is logged out from Chat-Api and isOwner
	public function logoutResets($user) {
		try {
			$removed = Users::where('phone', $this->user['phone'])->update([
				'instance_id' => null,
				'owns_instance' => 0
			]);
			if(!$removed) return false;
			
			Instances::where('id', $this->user['instance_id'])->update([
				'u_id' => null,
				'status' => 0
			]);
			return true;
		} catch(Exception $e) {
			Log::error("[ChatController:logoutResets] error: ".$e->getMessage());
			return false;			
		}
	}
	
	public function disablePortal($user)
	{
		try {
			 $portal = Portals::where('portal_id', $this->user['portal_id'])->update([
				'instances'=> 0,
				'remaining_instances'=>0
			]);
			if (!$portal) return false;
			
			return true;
		} catch (Exception $e){
			Log::error("[ChatController:disablePortal] error: ".$e->getMessage());
			return false;
		}
		
		
	}
	

	/**
	 * returns {array}
	 * ['status' => 'value', 'instance_id' => id ]
	 */
	public function inStatus(int $value, string $type) {
		$instance = ChatApi::getInstance($value, $type);
		if(!$instance) return false;

		$chat_url = ChatApi::instanceUrl('status', $instance);
		try {
			$status = json_decode(file_get_contents($chat_url));
			if(!@$status->accountStatus) return false;

			return ['status' => $status->accountStatus, 'instance_id' => $instance['id']];
		} catch(Exception $e) {
			Log::error("[HelperTrait:inStatus] ".$e->getMessage());
			return false;
		}
	}

	public function cachedWhatsApp($where) {
		try {
			$WhatsApp = WhatsApp::where($where)->first();
			return $WhatsApp ? $WhatsApp->toArray() : false;
		} catch(Exception $e) {
			Log::error("[HelperTrait:cachedDialogs] Error: ".$e->getMessage());
			return false;
		}
	}

	public function updateWhatsApp($options) {
		$options['updated_at'] = date("Y/m/d H:i:s");
		try {
			WhatsApp::updateOrInsert([
				'instance_id' => $options['instance_id'],
				'portal_id' => $options['portal_id']
			], $options);
			return true;
		} catch(Exception $e) {
			Log::error("[HelperTrait:updateWhatsApp] Error: ".$e->getMessage());
			return false;
		}
	}

	public function removeBlacklisted($dialogs, $portalId) {
		$blacklist = \App\Models\Blacklist::where('portal_id', $portalId)->get();
		if(!$blacklist) return $dialogs; // return all if no blacklist

		$blacklistNumbers = $blacklist->pluck('phone')->toArray();
		foreach($blacklistNumbers as $phoneNumber) {
			$chatId = $phoneNumber.'@c.us';
			if(isset($dialogs[$chatId])) {
				unset($dialogs[$chatId]);
			}
		}
		return $dialogs;
	}

	public function buildReceiveEvent($users, $options) {
		$hsApp = $this->hubspotApp->hsApp;
		$eventTypeId = $hsApp['config']['message_recieved'];
	
		if(count($users) == 1) {
			$user = $users[0];
			$data = [
				"id"=> $options['messageId'],
				"eventTypeId" => $eventTypeId,
				"user" => $user['name'],
				"message" => $options['message'],
				"objectId" => $user['objectId']
			];
		} else {
			$count = 0;
			$data['eventWrappers'] = [];
			foreach ($users as $user) {
				$event = [
					"id"=> $options['messageId'].$count,
					"eventTypeId" => $eventTypeId,
					"user" => $user['name'],
					"message" => $options['message'],
					"objectId" => $user['objectId']
				];
				$data['eventWrappers'][] = $event;
				$count++;
			}
		}
		return $data;
	}

	public function buildSendEvent($users, $options) {
		$hsApp = $this->hubspotApp->hsApp;
		$eventTypeId = $hsApp['config']['message_sent'];
	
		if(count($users) == 1) {
			$data = [
				"id"=> $options['messageId'],
				"eventTypeId" => $eventTypeId,
				"status" => $options['status'] ?? "sent",
				"message" => $options['message'],
				"objectId" => $users['objectId']
			];
		} else {
			$count = 0;
			$data['eventWrappers'] = [];
			foreach ($users as $user) {
				$event = [
					"id"=> $options['messageId'].$count,
					"eventTypeId" => $eventTypeId,
					"status" => $options['status'] ?? "sent",
					"message" => $options['message'],
					"objectId" => $user['objectId']
				];
				$data['eventWrappers'][] = $event;
				$count++;
			}
		}
		return $data;
	}

	public function buildStatusEvent($users, $options) {
		$hsApp = $this->hubspotApp->hsApp;
		$eventTypeId = $hsApp['config']['message_sent'];
	
		if(count($users) == 1) {
			$data = [
				"id"=> $options['messageId'],
				"eventTypeId" => $eventTypeId,
				"status" => $options['status'],
				"message" => $options['message'],
				"objectId" => $users[0]['objectId']
			];
		} else {
			$count = 0;
			$data['eventWrappers'] = [];
			foreach ($users as $user) {
				$event = [
					"id"=> $options['messageId'].$count,
					"eventTypeId" => $eventTypeId,
					"status" => $options['status'],
					"message" => $options['message'],
					"objectId" => $user['objectId']
				];
				$data['eventWrappers'][] = $event;
				$count++;
			}
		}
		return $data;
	}

	public function updateSendTimeline($portalId, $options) {
		$phone = str_replace('+', '', $options['phone']);
		$hsApp = $this->hubspotApp;

		$users = $hsApp->search($portalId, $phone);
		if(!$users) return;

		$users = $this->filterUsers($users);

		$eventUrl = 'timeline/event';
		if(count($users) > 1) $eventUrl.='/batch';

		$events = $this->buildSendEvent($users, $options);

		return $hsApp->updateTimeline($portalId, $events, $eventUrl);
	}

	public function filterUsers($contacts)  {
		$users = [];
		if(!$contacts) return $users;

		foreach($contacts as $contact) {
			$firstname = $contact->properties->firstname->value ?? '';
			$lastname = $contact->properties->lastname->value ?? '';
			$name = $firstname;
			if($lastname) $name.= ' '.$lastname;
			$phone = $contact->properties->phone->value ?? '';
			$users[] = array(
				'name' => $name ? $name : NULL,
				'email' => $contact->properties->email->value ?? '',
				'owner_id' => $contact->properties->hubspot_owner_id->value ?? '',
				'objectId' => $contact->vid
			);
		}
		return $users;
	}

	public function demoDialogs() {
		$dialogs[] = [
			"id" => "<EMAIL>",
			"phone" => "+91213433435",
			"time" => time(),
			"name" => "Demo User 1",
		];
		$dialogs[] = [
			"id" => "<EMAIL>",
			"phone" => "+99243233435",
			"time" => time(),
			"name" => "Demo User 2",
		];
		return $dialogs;
	}

	public function demoConversation() {
		$conversation[] = [
			'id' => 'true_918826881907@c.us_A6BBC502FDEDC589509C87934DD9115E',
		    'body' => 'Hi',
		    'fromMe' => true,
		    'self' => 0,
		    'isForwarded' => 0,
		    'author' => '<EMAIL>',
		    'time' => 1566880460,
		    'chatId' => '<EMAIL>',
		    'messageNumber' => 15,
		    'type' => 'chat',
		    'senderName' => 'Demo user',
		    'quotedMsgBody' => NULL,
		    'chatName' => 'Demo user',
		];
		$conversation[] = [
			'id' => 'false_918826881907@c.us_14872F97A6AA59A6F371DD89A5F2DEC7',
		    'body' => 'Yes',
		    'fromMe' => false,
		    'self' => 0,
		    'isForwarded' => 0,
		    'author' => '<EMAIL>',
		    'time' => 1566880503,
		    'chatId' => '<EMAIL>',
		    'messageNumber' => 16,
		    'type' => 'chat',
		    'senderName' => 'Demo user',
		    'quotedMsgBody' => NULL,
		    'chatName' => 'Demo user',
		];
		return $conversation;
	}

	public function getDialogs($time = '') {
		$dialog_id = $this->user['portal_id'].'.'.$this->user['instance_id'];
		
		// fetch blacklist 
		$blacklistNumbers = \App\Models\Blacklist::select('phone')->where('portal_id', $this->user['portal_id'])->get();
		$blacklistNumbers = $blacklistNumbers ? $blacklistNumbers->pluck('phone')->toArray(): [];
		foreach ($blacklistNumbers as $key => $value) {
			$blacklistNumbers[$key] = '+'.$value;
		}

		$time && $dialogs = DB::table('dialogs')
		->where([['dialog_id', '=', $dialog_id], ['time', '<', $time]])
		->whereNotIn('phone', $blacklistNumbers)
		->limit(100)->orderBy('time', 'desc')->get();

		!$time && $dialogs = DB::table('dialogs')
		->where('dialog_id', $dialog_id)
		->whereNotIn('phone', $blacklistNumbers)
		->limit(200)->orderBy('time', 'desc')->get();

		return $dialogs ? $dialogs->keyBy('id')->toArray() : [];
	}

	public function getAllDialogs($time = '') {
		$dialog_id = $this->user['portal_id'].'.'.$this->user['instance_id'];

		// fetch blacklist 
		$blacklistNumbers = \App\Models\Blacklist::select('phone')->where('portal_id', $this->user['portal_id'])->get();
		$blacklistNumbers = $blacklistNumbers ? $blacklistNumbers->pluck('phone')->toArray(): [];
		foreach ($blacklistNumbers as $key => $value) {
			$blacklistNumbers[$key] = '+'.$value;
		}

		$time && $dialogs = DB::table('dialogs')
		->where([['dialog_id', '=', $dialog_id], ['time', '<', $time]])
		->whereNotIn('phone', $blacklistNumbers)
		->orderBy('time', 'desc')->get();

		!$time && $dialogs = DB::table('dialogs')
		->where('dialog_id', $dialog_id)
		->whereNotIn('phone', $blacklistNumbers)
		->orderBy('time', 'desc')->get();

		return $dialogs ? $dialogs->keyBy('id')->toArray() : [];
	}

	public function updateDialogs($dialogs) {
		$dialog_id = $this->user['portal_id'].'.'.$this->user['instance_id'];
		foreach ($dialogs as $dialog) {
			$dialog['dialog_id'] = $dialog_id;
			// don't update name if name not found in history so name update from cron can be kept
			if(!$dialog['name']) unset($dialog['name']);
			try {
				$user = \App\Models\Dialogs::where([
					['id', '=', $dialog['id']],
					['dialog_id', '=', $dialog_id],
				])->first();
				if(!$user) {
					\App\Models\Dialogs::create($dialog);
					continue;
				}
				$user->time = $dialog['time'];
				if(strpos($user->id, 'g.us') !== false) {
					isset($dialog['name']) && $user->name = $dialog['name'];
				}
				$user->save();
			} catch(Exception $e) {
				\Log::error("[Func:updateDialogs] Exception ".$e->getMessage());
			}
		}
	}

	public function updateNewDialogs($dialogs, $dialog_id) {
		echo "Updating dialogs".PHP_EOL;
		foreach ($dialogs as $dialog) {
			if(!$dialog['name']) unset($dialog['name']);
			$dialog['dialog_id'] = $dialog_id;
			try {
				$user = \App\Models\Dialogs::where([
					['id', '=', $dialog['id']],
					['dialog_id', '=', $dialog_id],
				])->first();
				if(!$user) {
					\App\Models\Dialogs::create($dialog);
					continue;
				}
				if(!$user->name && isset($dialog['name']) && $dialog['name']) {
					$user->name = $dialog['name'];
					$user->save();
				}
			} catch (Exception $e) {
				Log::error("[HelperTrait:updateNewDialogs] Exception ".$e->getMessage());
			}
		}
		echo "Updating dialogs Done\n".PHP_EOL;
	}

	public function saveSentMessage($message) {
		(!isset($message['isForwarded']) || !$message['isForwarded']) && $message['isForwarded'] = 0;
		try {
			\App\Models\Messages::updateOrCreate(['id' => $message['id']], $message);
			return true;
		} catch(Exception $e) {
			Log::error("[HelperTrait:saveSentMessage] Exception: ".$e->getMessage());
			return false;
		}
	}

	public function saveFetchedMessages($messages, $dialogId) {
		try {
			$allMessages = [];
			foreach ($messages as $message) {
				$message = (array) $message;
				$message['dialogId'] = $dialogId;
				!isset($message['caption']) && $message['caption'] = null;
				(!isset($message['isForwarded']) || !$message['isForwarded']) && $message['isForwarded'] = 0;
				$allMessages[] = $message;
			};
			DB::table('messages')->insertOrIgnore($allMessages);
		} catch(Exception $e) {
			Log::error("[HelperTrait:saveFetchedMessages] Exception ".$e->getMessage());
		}
	}

	public function syncFetchedMessages($messages, $dialogId) {
		foreach ($messages as $message) {
			$message = (array) $message;
			$message['dialogId'] = $dialogId;
			$this->saveSentMessage($message);
		}
	}

	public function getSavedMessages($args, $dialogId) {
		$where = [['dialogId', '=', $dialogId]];
		// $messages = \App\Models\Messages::where($where)->orderBy('time', 'desc')->limit(100)->get();
		isset($args['chatId']) && $where[] = ['chatId', '=', $args['chatId']];
		isset($args['fromMe']) && $where[] = ['fromMe', '=', $args['fromMe']];
		isset($args['max_time']) && $where[] = ['time', '<', $args['max_time']];
		isset($args['min_time']) && $where[] = ['time', '>', $args['min_time']];
		$messages = DB::table('messages')
				->selectRaw("messages.*, users.name")
				->where($where)
				->leftJoin('users', 'messages.uid', 'users.id')
				->orderBy('time', 'desc')->limit(100)->get();
		return $messages ? $messages->sortBy('time') : [];
	}

	public function usersFromContact($contact) {
		$users = [];
		$firstname = $contact->properties->firstname->value ?? '';
		$lastname = $contact->properties->lastname->value ?? '';
		$email = $contact->properties->email->value ?? '';
		$name = $firstname;
		if($lastname) $name.= ' '.$lastname;

		// add user
		$user['name'] = $name;
		$user['objectId'] = $contact->vid;
		$user['email'] = $email;
		$users[] = $user;
		return $users;
	}

	public function usersFromInput($input) {
		$users = [];
		$firstname = $input['firstname'] ?? '';
		$lastname = $input['lastname'] ?? '';
		$email = $input['email'] ?? '';
		$name = $firstname;
		if($lastname) $name.= ' '.$lastname;

		// add user
		$user['name'] = $name;
		$user['objectId'] = $input['associatedObjectId'];
		$user['email'] = $email;
		$users[] = $user;
		return $users;
	}

	public function applyContactTokens($contact, $params) {
		$properties = [];
		foreach($contact->properties as $key => $prop) {
			$properties[$key] = $prop->value ?? '';
		}

		$paramList = [];
		$paramParts = explode('|', $params);
		foreach ($paramParts as $item) {
			$item = trim($item);
			$key = preg_replace('/\[|\]/', '', $item);
			if(isset($properties[$key]) && $properties[$key]) {
				$paramList[] = $properties[$key];
			} else {
				$paramList[] = $key;
			}
		}
		return implode('|', $paramList);
	}
}
