services:
  db:
    build:
      context: ./docker/db
    container_name: db
    ports:
      - "3306:3306"
    volumes:
      - mysqldb:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=!nothing!
  php:
    container_name: slim_php
    build:
      context: .
      dockerfile: ./docker/php/Dockerfile
    ports:
      - '9000:9000'
    env_file:
      - '.env'
    volumes:
      - './logs:/var/www/html/logs'
      - ./app:/var/www/html/app
      - ./routes:/var/www/html/routes
      - ./resources:/var/www/html/resources
      - ./.env:/var/www/html/.env
      - ./bootstrap:/var/www/html/bootstrap
      - ./public:/var/www/html/public
    depends_on:
      - db
  nginx:
    container_name: slim_nginx
    image: nginx:stable-alpine
    ports:
      - '80:80'
    volumes:
      - .:/var/www/html
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      UPLOAD_LIMIT: 10G
    ports:
      - '8080:80'
    depends_on:
      - db

volumes:
  mysqldb: {}