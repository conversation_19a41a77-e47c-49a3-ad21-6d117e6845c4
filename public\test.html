<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RMS CRM Series - Server Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .test-links {
            margin: 20px 0;
        }
        .test-links a {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .test-links a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Server Test Successful!</h1>
        
        <div class="success">
            <strong>Great!</strong> Your server can serve static files from the /rmscrmseries/ directory.
        </div>
        
        <h2>Next Steps:</h2>
        <ol>
            <li>Test PHP processing</li>
            <li>Test Laravel routing</li>
            <li>Test Vue.js application</li>
        </ol>
        
        <div class="test-links">
            <h3>Test Links:</h3>
            <a href="phpinfo.php" target="_blank">Test PHP Processing</a>
            <a href="index.php" target="_blank">Test Laravel Application</a>
            <a href="../rmscrmseries/" target="_blank">Test Full Application</a>
        </div>
        
        <h2>Current URL Structure:</h2>
        <ul>
            <li><strong>Base URL:</strong> https://api.niswey.net/rmscrmseries/</li>
            <li><strong>Static Files:</strong> Working ✅</li>
            <li><strong>PHP Processing:</strong> <a href="phpinfo.php">Test Here</a></li>
            <li><strong>Laravel Routes:</strong> <a href="index.php">Test Here</a></li>
        </ul>
        
        <h2>If Laravel isn't working:</h2>
        <ol>
            <li>Check that the <code>public</code> directory contains <code>index.php</code></li>
            <li>Verify <code>.htaccess</code> file exists and is readable</li>
            <li>Ensure Apache mod_rewrite is enabled</li>
            <li>Check file permissions (755 for directories, 644 for files)</li>
            <li>Review Apache error logs</li>
        </ol>
    </div>
</body>
</html>
