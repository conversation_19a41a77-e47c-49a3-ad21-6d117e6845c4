server {
	listen 443 ssl;

	index index.php;
	server_name localhost;
	root /var/www/html/public;
	error_log /var/log/nginx/error.log;
	access_log /var/log/nginx/access.log;

	location / {
		try_files $uri /index.php$is_args$args;
	}

	location ~ ^/index\\.php(/|$) {
		fastcgi_pass php:9000;
		fastcgi_split_path_info ^(.+\\.php)(/.*)$;
		include fastcgi_params;

		fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
		fastcgi_param DOCUMENT_ROOT $realpath_root;
		fastcgi_param HTTPS on;

		fastcgi_buffer_size 128k;
		fastcgi_buffers 4 256k;
		fastcgi_busy_buffers_size 256k;

		internal;
	}

	location ~ \\.php$ {
		return 404;
	}
}

server {
	if ($host = localhost) {
		return 301 https://$host$request_uri;
	} # managed by Certbot

	listen 80 ;
	listen [::]:80 ;
	server_name localhost;
	return 404; # managed by Certbot
}
