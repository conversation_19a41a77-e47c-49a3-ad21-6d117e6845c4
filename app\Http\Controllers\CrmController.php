<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use File;
use Storage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmUsers, CrmData, CrmCompany, CrmCompanyPrimaryReps, Warranty, CrmContact, CrmEquipment, CrmEquipmentWar, CrmContactTemp};
use Carbon\Carbon;
use PDO;

class CrmController extends Controller
{

    public function getToken(Request $request){
        $input = $this->validate($request, [
          'username' => 'required',
          'password' => 'required'
        ]);
        $res = Crm::getToken($input);
    }
    
    public function webhook(Request $request) {
        $input = $request->input();
        Log::info("[Crm:Webhook] payload ".json_encode($input));
        dd();
        $data = [];
        $portalId = $input['portal-id'];
        $type = '';
        $properties = $input['properties'];
        $data['portalId'] = $portalId;
        if(isset($input['is-contact']) &&  $input['is-contact'] == true){
            $type = 'contact';
            $data['firstName'] = $properties['firstname']['value'] ?? '';
            $data['middleName'] = $properties['middlename']['value'] ?? '';
            $data['lastName'] = $properties['lastname']['value'] ?? '';
            $data['phone'] = $properties['phone']['value'] ?? '';
            $data['email'] = $properties['email']['value'] ?? '';
            $res = Crm::updateContactCrm($data);
        } else if($input['objectType'] == 'COMPANY'){
            $type = 'company';
            $data['companyName'] = $properties['name']['value'] ?? '';
            $data['address1'] = $properties['address']['value'] ?? '';
            $data['city'] = $properties['city']['value'] ?? '';
            $data['state'] = $properties['state']['value'] ?? '';
            $data['phone'] = $properties['phone']['value'] ?? '';
            $data['email'] = $properties['email']['value'] ?? '';
            $res = Crm::updateCompanyCrm($data);
        } else if($input['objectType'] == 'DEAL'){
            $type = 'company';
            $data['description'] = $properties['dealname']['value'] ?? '';
            $data['startDate'] = $properties['createdate']['value'] ?? '';
            $res = Crm::updateDealCrm($data);
        }
        
        try {
            CrmData::insert(['portal_id' => $portalId, 
                'type' => $type,
                'from' => 'hubspot',
                'data' => json_encode($input),
                'created_at' => date('Y-m-d H:i:s'),
                'status' => 1]
            );
        } catch (Exception $e) {
            Log::error("[CrmController:webhook] Exception: ".$e->getMessage());
        }
	}

    function csvToArray($name,$filename = '', $delimiter = ','){
        $filename = base_path('csv/'.$name.'.csv');
        
        if (!file_exists($filename) || !is_readable($filename))
            return false;

        $header = null;
        $data = array();
        if (($handle = fopen($filename, 'r')) !== false)
        {
            while (($row = fgetcsv($handle, 1000, $delimiter)) !== false)
            {
                if (!$header)
                    $header = $row;
                else
                    $data[] = array_combine($header, $row);
            }
            fclose($handle);
        }
        return $data;
    }

    public function test() {
        echo "ok";
    }

}
