@echo off
echo Building for production deployment...

REM Copy production environment file
copy .env.production .env

REM Clear Laravel caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

REM Install dependencies
call npm install

REM Clean previous builds
rmdir /s /q public\js 2>nul
rmdir /s /q public\css 2>nul

REM Build assets for production with correct environment
call npm run production

REM Optimize Laravel for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo Production build completed!
echo.
echo Next steps:
echo 1. Upload the entire project to your server
echo 2. Make sure the web server points to the 'public' directory
echo 3. Ensure the .htaccess file is properly configured
echo 4. Set proper file permissions (755 for directories, 644 for files)
echo 5. Make sure storage and bootstrap/cache directories are writable
echo.
pause
