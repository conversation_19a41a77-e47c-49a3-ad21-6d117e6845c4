<?php 

namespace App\Helpers;

class Params {

	static $user = [
		'name' => 'Praveen yadav',
		'email' => '<EMAIL>',
		'portal_id' => 2720327,
		'portal_name' => 'Niswey',
		'phone' => '918826881907',
	];

	static $another_user = [
		'email' => '<EMAIL>',
		'name' => 'Dhiraj pandey',
		'portal_id' => 2720327
	];

	static function set() {
		//set user_id to user
		$user = self::$user;
		$user['user_id'] = base64_encode($user['email'].'|'.$user['portal_id']);
		self::$user = $user;

		//set user_id to another user
		$another_user = self::$another_user;
		$another_user['user_id'] = base64_encode($another_user['email'].'|'.$another_user['portal_id']);
		self::$another_user = $another_user;
	}

	static function createUser() {
		return self::$user;
	}

	static function saveUser() {
		return [
			'user_id' => self::$user['user_id'],
			'portal_id' => self::$user['portal_id'],
			'instance_id' => 1,
		];
	}

	static function joinUser() {
		return self::$another_user;
	}

	static $data = [
		'timeline' => [
			'apiTest' => true,
	  		'user_id' => '************************',
	  		'portal_id' => 2720327,
	  		'messages' => [
	  			[
	  				'id' => 'false_919818593192-1442667610@g.us_DB311E4D65CCA43CC63D4E563B6900ED',
					'body' => 'Multiple phone works',
					'fromMe' => false,
					'self' => 0,
					'isForwarded' => 0,
					'author' => '<EMAIL>',
					'time' => 1562752501,
					'chatId' => '<EMAIL>',
					'messageNumber' => 1,
					'type' => 'chat',
					'senderName' => 'API tester',
					'chatName' => NULL,
	  			]
	  		]
	  	],
	  	'workflow' => [
	  		'apiTest' => true,
	  		'origin' => [
	  			'portalId' => 2720327,
			    'extensionDefinitionId' => 722,
			    'extensionDefinitionVersionId' => 2,
	  		],
	  		'object' => [
	  			'objectId' => 3054101,
			    'propertyValues' => [],
			    'objectType' => 'CONTACT'
	  		],
	  		'fields' => [
	  			'message' => 'Hello Test',
			    'file_url' => 'https://www.gettyimages.in/gi-resources/images/500px/983794168.jpg',
	  		]
	  	],
	  	'send' => [
	  		'apiTest' => true,
	  		'user_id' => '************************',
	  		'message' => 'Api single Test',
	  		'url' => 'https://app.hubspot.com/contacts/2720327/contact/3054101/',
			'instance_id' => 1
	  	],
	  	'send_list' => [
	  		'apiTest' => true,
	  		'user_id' => '************************',
	  		'message' => 'Api list Test',
	  		'url' => 'https://app.hubspot.com/contacts/2720327/contact/3054101/',
			'instance_id' => 1,
			'list' => 347,
			'users' => '918826881907'
	  	]
	];

	static $methods = [
		'timeline' => 'POST',
		'workflow' => 'POST',
		'instance_status' => 'POST',
		'send' => 'POST',
		'send_list' => 'POST',
	];

	static function getParams($action) {
		if($action == 'timeline') {
			self::$data['timeline']['messages'][0]['time'] = time();
			self::$data['timeline']['messages'][0]['id'] = time();
		}
		return self::$data[$action];
	}

	static function method($action) {
		return self::$methods[$action];
	}
}


Params::set();
