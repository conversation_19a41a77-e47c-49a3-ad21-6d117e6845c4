import { ref } from 'vue'

export function useApi() {
  const loading = ref(false)
  const error = ref(null)

  const apiCall = async (url, options = {}) => {
    loading.value = true
    error.value = null

    try {
      // Ensure URL is absolute for subdirectory deployment
      const baseUrl = window.APP_URL || ''
      const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`

      const response = await fetch(fullUrl, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          ...options.headers
        },
        ...options
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`)
      }

      return data
    } catch (err) {
      error.value = err.message
      console.error('API Error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const get = (url, params = {}) => {
    const urlParams = new URLSearchParams(params)
    const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url
    return apiCall(fullUrl, { method: 'GET' })
  }

  const post = (url, data = {}) => {
    return apiCall(url, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  return {
    loading,
    error,
    get,
    post,
    apiCall
  }
}
