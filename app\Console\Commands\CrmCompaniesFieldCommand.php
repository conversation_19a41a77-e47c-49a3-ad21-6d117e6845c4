<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmCompanyField,CrmPortals};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmCompaniesFieldCommand extends Command
{

    protected $signature = "crm:companies-field";

    protected $description = "Get Rms fields for companies";

    public function handle()
    {
        
            $data = [];
            $portals = CrmPortals::get();

            foreach ($portals as $portal) {
                $data['portalId'] = $portal->portal_id;
                $data['fromDate'] =  '2019-01-01'; //date("Y-m-d", time() - 86400);
                $data['NoteIds'] = [];
                $data['CompanyIds'] = [];
                $start = microtime(true);
                $resData = Crm::getCompanies($data,true);
                $end = microtime(true);
                $duration = $end - $start;
                Log::info("[CrmCompaniesFieldCommand:handle] getCompanies took {$duration} seconds");
                
                if (isset($resData) && !empty($resData)) {
                    // foreach ($resData as $res) {
                        if (isset($resData->data->items) && !empty($resData->data->items)) {
                             $firstItem = $resData->data->items[0];

                            $fieldKeys = array_keys((array)$firstItem);

                            foreach ($fieldKeys as $fieldName) {
                                $value = $firstItem->$fieldName ?? null;
                                $fieldType = gettype($value);

                                CrmCompanyField::updateOrInsert(
                                    ['field_name' => $fieldName],
                                    [
                                        'field_name' => $fieldName,
                                        'field_type' => $fieldType,
                                        'created_at' => date('Y-m-d H:i:s'),
                                        'updated_at' => date('Y-m-d H:i:s')
                                    ]
                                );

                                Log::info("[CrmCompaniesFieldCommand:handle] Insert field: {$fieldName} with type: {$fieldType}");
                            }
                            return;
                        }
                    // }
                    
                } else {
                    Log::info("[CrmCompaniesFieldCommand:handle] No data for " . $data['fromDate']);
                }
            }
    }
}
