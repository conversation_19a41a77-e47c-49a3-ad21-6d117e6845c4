{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env APP_ENV=dev mix", "staging": "cross-env APP_ENV=staging mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "devDependencies": {"@vue/compiler-sfc": "^3.5.14", "autoprefixer": "^10.4.14", "axios": "^0.21", "cross-env": "^7.0.3", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "vue": "^3.5.14", "vue-loader": "^17.0.0"}, "dependencies": {"promise": "^8.1.0", "vue-router": "^4.5.1"}}