<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\CrmCompaniesCommand::class,
        Commands\CrmContactsCommand::class,
        Commands\CrmEquipmentsCommand::class,
        Commands\CrmOwnersCommand::class,
        Commands\CrmDealsCommand::class,
        Commands\CrmDealOwnersCommand::class,
        Commands\CrmDealNamesCommand::class,
        Commands\CrmDealFixCommand::class,
        Commands\CrmContactsFixCommand::class,
        Commands\CrmCompanyFixCommand::class,
        Commands\CrmMarketingCommand::class,
        Commands\HubspotDeals::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('crm:companies')->everySixHours();
        $schedule->command('crm:contacts')->everySixHours();
        $schedule->command('crm:equipments')->daily();
        $schedule->command('crm:marketing')->everySixHours();
        $schedule->command('crm:owners')->daily();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
