<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\Hubspot;
use App\Models\CrmEquipment;
use App\Models\HubspotDeal;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class HubspotDeals extends Command
{
    protected $signature = "hubspot:deals";

    protected $description = "Get Hubspot deals details";

    public function handle()
    {
        $response = Hubspot::getAllDeals();
        foreach ($response as $deal) {
            $equipment = CrmEquipment::select('hubspot_deal_id')->where('hubspot_deal_id', $deal->hs_object_id)->first();
            $primary_deal = 0;
            if(isset($equipment->hubspot_deal_id)) {
                $primary_deal = 1;
            }
            HubspotDeal::updateOrCreate(
                ['hs_object_id' => $deal->hs_object_id],
                [
                    'primary_deal' => $primary_deal,
                    'equipment_id' => $deal->equipment_id,
                    'hs_lastmodifieddate' => $deal->hs_lastmodifieddate,
                    'createdate' => $deal->createdate,
                    'parent_name' => $deal->parent_name,
                    'hubspot_id' => $deal->hubspot_id,
                    'description' => $deal->description,
                    'start_date___date_picker' => $deal->start_date___date_picker,
                    'start_hours___numeric' => $deal->start_hours___numeric,
                    'lastsmrdate___date_picker' => $deal->lastsmrdate___date_picker,
                    'lastsmr_date___numeric' => $deal->lastsmr_date___numeric,
                    'latitude' => $deal->latitude,
                    'longitude' => $deal->longitude,
                    'hours_per_day' => $deal->hours_per_day,
                    'hours_per_day_range' => $deal->hours_per_day_range,
                    'inventory' => $deal->inventory,
                    'fleet' => $deal->fleet,
                    'fleet_type' => $deal->fleet_type,
                    'status' => $deal->status,
                    'new_used' => $deal->new_used,
                    'cbgrp' => $deal->cbgrp,
                    'hours_per_day_override' => $deal->hours_per_day_override,
                    'uc_target_wear_percent' => $deal->uc_target_wear_percent,
                    'exclude_uc_tracking' => $deal->exclude_uc_tracking,
                    'complete' => $deal->complete,
                    'satellite_identifier' => $deal->satellite_identifier,
                    'parent_id' => $deal->parent_id,
                    'parent_type' => $deal->parent_type,
                    'cbmod' => $deal->cbmod,
                    'cbmyr' => $deal->cbmyr,
                    'cbmak' => $deal->cbmak,
                    'serial_no_' => $deal->serial_no_,
                    'cbord' => $deal->cbord,
                    'equipment_number' => $deal->equipment_number,
                    'category_id' => $deal->category_id,
                    'stdwtdenddate' => $deal->stdwtdenddate,
                    'extwarrantyenddate' => $deal->extwarrantyenddate,
                    'warranty_type___multi_select' => $deal->warranty_type___multi_select,
                    'basicwarrantyhrs' => $deal->basicwarrantyhrs,
                    'extwarrantyhrs' => $deal->extwarrantyhrs,
                    'lease_expiry_date___date_picker' => $deal->lease_expiry_date___date_picker,
                    'tier' => $deal->tier,
                    'dealname' => $deal->dealname
                ]
            );
        }
    }

    //     $subQuery = HubspotDeal::select('equipment_id')
    //         ->whereNotIn('equipment_id', [175, 75, 24040])
    //         ->groupBy('equipment_id')
    //         ->havingRaw('COUNT(*) > 1');

    //     // Main query
    //     $deals = HubspotDeal::select('hubspot_deals.hs_object_id')
    //         ->joinSub($subQuery, 'h2', function ($join) {
    //             $join->on('hubspot_deals.equipment_id', '=', 'h2.equipment_id');
    //         })
    //         ->where('primary_deal', 0)
    //         ->get();

    //     foreach ($deals as $deal) {
    //         $dealId = $deal->hs_object_id;
    //         Hubspot::deleteDeal($dealId);
    //     }
    // }
}
