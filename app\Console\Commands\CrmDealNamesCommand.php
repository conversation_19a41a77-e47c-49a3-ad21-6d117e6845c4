<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmData, CrmContact, CrmCompany, CrmCompanyPrimaryReps, CrmEquipment, CrmOwners, CrmEquipmentAll};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmDealNamesCommand extends Command
{

    protected $signature = "crm:dealnames";

    protected $description = "Update all hubspot deal names";

    public function handle()
    {

        $portals = CrmPortals::first();
        $portal_id = '';
        if($portals){
            $portal_id = $portals->portal_id;
        }

        CrmEquipment::chunkById(200, function ($equipments) use ($portal_id) {
                foreach ($equipments as $items) {
                    $dealid = $items->hubspot_deal_id;
                    $deal = Hubspot::getDeal($portal_id, $dealid);
                    if(!$deal){
                        $this->error("deal with id $dealid could not fetch deal");
                        continue;
                    }
                    $eqyear = $deal->properties->cbmyr->value??"";
                    $make = $deal->properties->cbmak->value??"";
                    $model = $deal->properties->cbmod->value??"";
                    $srno = $deal->properties->serial_no_->value??"";
                    $companyname = $deal->properties->parent_name->value??"";
                    $name=$eqyear."-".$make."-".$model."-".$srno."-".$companyname;

                    $props = Hubspot::buildDealProps([
                        'dealname' => $name
                    ]);

                    $response = Hubspot::updateDeal($portal_id, $dealid, $props);

                    if ($response === false) {
                        Log::error("[CrmDealNamesCommand:handle] Deal updation failed: " . json_encode($response));
                        $this->error("deal with id $dealid update unsuccessful");
                        continue;
                    }
                    $this->info("deal with id $dealid updated successfully"); 
                }
            }, $column = 'id');

        $this->info("success");
    }
}
