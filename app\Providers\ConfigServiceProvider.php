<?php

namespace App\Providers;

use App\Helpers\Options;
use Illuminate\Support\ServiceProvider;

class ConfigServiceProvider extends ServiceProvider
{
    
    public function register()
    {
        $this->app->bind('on', function() {
        	return new Options;
        });

        $file = base_path('app/Helpers/Helpers.php');
        if(file_exists($file)) {
        	require_once($file);
        }

        if (method_exists($this, 'loadViewsFrom')) {
            $this->loadViewsFrom(base_path().'/resources/views', 'custom-log-viewer');
        }
        
    }

}
