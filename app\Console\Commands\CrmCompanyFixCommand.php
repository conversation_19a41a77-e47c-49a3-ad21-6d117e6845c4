<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmData, CrmContact, CrmCompany, CrmCompanyPrimaryReps, CrmEquipment, CrmOwners, CrmEquipmentAll};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmCompanyFixCommand extends Command
{

    protected $signature = "crm:companyfix";

    protected $description = "Update all hubspot contact owners";

    public function handle()
    {
        $startDate = '2017-01-01';
        $start_time = '00:00:00';
        $endDate = '2025-02-05';
        $end_time = '23:59:59';

        $start_datetime = $startDate . ' ' . $start_time;
        $end_datetime = $endDate . ' ' . $end_time;
        CrmCompany::whereBetween('updated_at', [$start_datetime, $end_datetime])->chunkById(200, function ($equipments) {
            foreach ($equipments as $items) {
                $hubspotcompanyid = $items->hubspot_company_id ?? "";
                $companyId = $items->crm_company_id ?? "";

                if (!$hubspotcompanyid || !$companyId) {
                    continue;
                }

                $company_owner = "";
                if (!empty($companyId)) {
                    $getCompanyPrimaryReps = Crm::getCompanyPrimaryReps($companyId);
                    if (!empty($getCompanyPrimaryReps)) {
                        foreach ($getCompanyPrimaryReps as $companyPrimaryReps) {
                            $owner_email = $companyPrimaryReps->email;
                            $role = $companyPrimaryReps->role;
                            if ($role == "Komatsu Rep") {
                                $owner = CrmOwners::where('email', $owner_email)->first();
                                if (isset($owner->owner_id)) {
                                    $company_owner = $owner->owner_id;
                                }
                                break;
                            }
                        }
                    }
                }

                $props = Hubspot::buildCompanyProps([
                    'hubspot_owner_id' => $company_owner
                ]);

                $response = Hubspot::updateCompany(722284, $hubspotcompanyid, $props);

                if ($response === false) {
                    // Log::error("[CrmCompanyFixCommand:handle] Company updation failed: " . json_encode($props));
                    $this->error("company with id $companyId update unsuccessful");
                    continue;
                } else {
                    try {
                        CrmCompany::updateOrCreate(
                            [
                                'hubspot_company_id' => $hubspotcompanyid,
                                'crm_company_id' => $companyId
                            ],
                            [
                                'hubspot_company_id' => $hubspotcompanyid,
                                'crm_company_id' => $companyId,
                                'company_owner_id' => $company_owner,
                                'updated_at' => date('Y-m-d H:i:s')
                            ]
                        );
                        $this->info("company with id $companyId updated successfully");
                    } catch (Exception $e) {
                        Log::error("[CrmCompanyFixCommand:handle] Exception: " . $e->getMessage());
                        $this->error("company with id $companyId db update unsuccessful");
                    }
                }
            }
        }, $column = 'id');

        $this->info("success");
    }
}
