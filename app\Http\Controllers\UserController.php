<?php

namespace App\Http\Controllers;

use Log;
use Exception;
use Illuminate\Http\Request;
use App\Models\User;
use App\Helpers\Hubspot;
use Illuminate\Support\Facades\Crypt;

class UserController extends Controller
{

    public function getUsersDetail(Request $request){
        try {
            $input = $request->input();

            // Validate required parameters
            if (!isset($input['portal_id']) || !isset($input['user_id'])) {
                return $this->jsonError([
                    'error' => 'portal_id and user_id are required parameters'
                ]);
            }

            // Decrypt user_id from URL parameter
            $userId = Crypt::decryptString($input['user_id']);

            if (!$userId) {
                return $this->jsonError([
                    'error' => 'Invalid user_id parameter'
                ]);
            }

            // Find user by portal_id and user_id
            $user = User::where('portal_id', $input['portal_id'])
                       ->where('user_id', $userId)
                       ->first();

            if (!$user) {
                return $this->jsonError([
                    'error' => 'User not found'
                ]);
            }

            // Check if user has Admin role for wizard access
            $hasAccess = $user->role === 'Admin';

            Log::info('[UserController:getUsersDetail] User: ' . $user->email . ', Role: ' . $user->role . ', Access: ' . ($hasAccess ? 'granted' : 'denied'));

            return $this->jsonOk([
                'data' => $user,
                'hasAccess' => $hasAccess,
                'role' => $user->role
            ]);

        } catch (Exception $e) {
            Log::error('[UserController:getUsersDetail] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching user details',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getUsers(Request $request){
        try {
            $input = $request->input();
            //Get User
            $response = Hubspot::fetchUsers($input['portal_id']);

            if ($response === false) {
                return $this->jsonError([
                    'error' => 'Failed to fetch users from HubSpot'
                ]);
            }

            $savedUsers = User::all()->keyBy('email'); // Assuming emails are unique
            $users = collect($response->results)->map(function ($hubUser) use ($savedUsers) {
                $email = $hubUser->email;
                return [
                    'user_id' => $hubUser->userId,
                    'name' => "{$hubUser->firstName} {$hubUser->lastName}",
                    'email' => $email,
                    'status' => $savedUsers[$email]->role ?? ''
                ];
            });
            Log::info('[UserController:getUsers] : ' .json_encode($users));
            return $this->jsonOk(['data' => $users]);

        } catch (Exception $e) {
            Log::error('[UserController:getUsers] Exception: ' . $e->getMessage());
            return $this->jsonError([
                'error' => 'An error occurred while fetching users',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function saveUsers(Request $request)
    {

        $this->validate($request, [
			'portal_id' => 'required'
		]);

        $users = $request->input('users');

        foreach ($users as $user) {
            $userFullName = explode(' ', $user['name']);
            User::updateOrCreate(
                ['email' => $user['email'], 'portal_id' => $request->input('portal_id')],
                ['user_id' => $user['user_id'],'first_name' => $userFullName[0],'last_name' => $userFullName[1], 'role' => $user['status'],'portal_id' => $request->input('portal_id')]
            );
        }
        Log::info('[UserController:saveUser] : Save User');
        return $this->jsonOk(['message' => 'Users saved successfully.']);
    }
}
