<template>
  <div>
    <div class="h-10 bg-gradient-to-r from-cyan-500 to-green-400"></div>

    <div class="flex justify-between items-center bg-gray-800 text-white h-8 px-4">
      
      <div class="flex items-center">
        <img :src="getAssetUrl('images/HubSpot.png')" alt="HubSpot Logo" style="height: 30px">
      </div>

      <div class="flex items-center space-x-2">
        <div class="bg-gray-700 p-1 rounded-full">
          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M10 3a5 5 0 100 10 5 5 0 000-10zM2 17a8 8 0 0116 0H2z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div class="text-sm text-white">
        {{ email }}
        </div>
        <button class="focus:outline-none">
          <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5.5 7l4.5 4.5L14.5 7h-9z" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useRoute } from 'vue-router';
import { computed } from 'vue';
import { useAssets } from '../composables/useAssets';

const route = useRoute();
const { getAssetUrl } = useAssets();

const email = computed(() => route.query.email || '');
</script>