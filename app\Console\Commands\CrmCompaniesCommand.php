<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmData, CrmCompany, CrmCompanyPrimaryReps, CrmUsers, CrmOwners, CrmCompanyFieldMapping};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmCompaniesCommand extends Command
{

    protected $signature = "crm:companies";

    protected $description = "Get Crm data for companies";

    public function handle()
    {
        // Crm::refreshToken(722284); exit;
        // $checkFlag = CrmData::select('status')
        //     ->where('type', '=', 'company')
        //     ->where('status', '=', '1')
        //     ->where('from', '=', 'crm')
        //     ->whereDate('created_at', today())->get();

        // if (!isset($checkFlag[0]->status)) {
            $data = [];
            $portals = CrmPortals::get();


            foreach ($portals as $portal) {
                $companyMappingFields = [];
                $companyMapFields = CrmCompanyFieldMapping::where('portal_id', $portal->portal_id)->get();
                
                if($companyMapFields){
                    foreach($companyMapFields as $companyMapField){
                        $companyMappingFields[$companyMapField->hubspot_field] = $companyMapField->rms_field;
                    }
                }
                
                // dd($companyMappingFields);

                $data['portalId'] = $portal->portal_id;
                $data['fromDate'] = '2025-01-01'; //date("Y-m-d", time() - 86400);
                $data['NoteIds'] = [];
                $data['CompanyIds'] = [];
                $resData = Crm::getCompanies($data);

                if (isset($resData) && !empty($resData)) {
                    foreach ($resData as $res) {
                        if (isset($res->data->items) && !empty($res->data->items)) {
                            foreach ($res->data->items as $items) {
                                $companyId = $items->companyId ?? "";
                                $company_owner = "";

                                if (!empty($companyId)) {
                                    $getCompanyPrimaryReps = Crm::getCompanyPrimaryReps($companyId,$portal->portal_id);
                                    if (!empty($getCompanyPrimaryReps)) {
                                        foreach ($getCompanyPrimaryReps as $companyPrimaryReps) {
                                            $owner_email = $companyPrimaryReps->email;
                                            $role = $companyPrimaryReps->role;
                                            if ($role == "Komatsu Rep") {
                                                $owner = CrmOwners::where('email', $owner_email)->first();
                                                if (isset($owner->owner_id)) {
                                                    $company_owner = $owner->owner_id;
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                                // $props = Hubspot::buildCompanyProps([
                                //     'parent_id' => $items->parentId ?? 0,
                                //     'record_type_id' => $items->recordTypeId ?? 0,
                                //     'branchid' => $items->branchId ?? "",
                                //     'cuco' => $items->companyName ?? "",
                                //     'name' => $items->companyName ?? "",
                                //     'company_name' => $items->companyName ?? "",
                                //     'legal_name' => $items->legalName ?? "",
                                //     'accountnumber' => $items->accountNo ?? "",
                                //     'address' => $items->address1 ?? "",
                                //     'address2' => $items->address2 ?? "",
                                //     'city' => $items->city ?? "",
                                //     'state' => $items->state ?? "",
                                //     'zip' => $items->zip ?? "",
                                //     'cucty' => $items->county ?? "",
                                //     'country' => $items->country ?? "",
                                //     'mailing' => $items->mailing ?? 0,
                                //     'latitude' => $items->latitude ?? 0,
                                //     'longitude' => $items->longitude ?? 0,
                                //     'phone' => $items->phone ?? "",
                                //     'fax' => $items->fax ?? "",
                                //     'website' => $items->web ?? "",
                                //     'linkedin_company_page' => $items->linked ?? 0,
                                //     'source_id' => $items->sourceId ?? 0,
                                //     'status' => $items->status ?? "",
                                //     'crmseriescompanyid' => $items->companyId ?? "",
                                //     'deleted' => $items->deleted ?? 0,
                                //     'last_modified' => $items->lastModified ?? "",
                                //     'entered' => $items->entered ?? "",
                                //     'cubr' => $items->branch ?? "",
                                //     'source' => $items->source ?? "",
                                //     'record_type' => $items->recordType ?? "",
                                //     'hubspot_id' => $items->hubspotId ?? 0,
                                //     'hubspot_owner_id' => $company_owner
                                // ]);

                                // $props = Hubspot::buildCompanyProps($companyMappingFields);
                                $props = Hubspot::buildCompanyPropsFromMapping($companyMappingFields, $items);

                                $checkCompany = CrmCompany::where('crm_company_id', $companyId)->first();
                                if (isset($checkCompany->hubspot_company_id)
                                &&
                                Hubspot::companyExists($checkCompany->hubspot_company_id, $data['portalId'])) {
                                    $hubSpotCompanyId = $checkCompany->hubspot_company_id;
                                    $response = Hubspot::updateCompany(
                                        $data['portalId'],
                                        $hubSpotCompanyId,
                                        $props
                                    );
                                } else {
                                    Log::info("[HS]Creating company: either ID is missing or company not found in HubSpot");
                                    $response = Hubspot::createCompany(
                                        $data['portalId'],
                                        $props
                                    );
                                }

                                if ($response === false) {
                                    Log::error("[CrmCompaniesCommand:handle] Company $companyId creation/updation failed");
                                } else {
                                    try {
                                        CrmCompany::updateOrCreate(
                                            [
                                                'crm_company_id' => $companyId,
                                                'hubspot_company_id' => $response->companyId,
                                            ],
                                            [
                                                'crm_company_id' => $companyId,
                                                'hubspot_company_id' => $response->companyId,
                                                'company_owner_id' => $company_owner,
                                                'created_at' => date('Y-m-d H:i:s'),
                                                'updated_at' => date('Y-m-d H:i:s'),
                                            ]
                                        );
                                    } catch (Exception $e) {
                                        Log::error("[CrmCompaniesCommand:handle] Exception: " . $e->getMessage());
                                    }
                                }
                            }
                        }
                    }
                    try {
                        $status = 0;
                        if (isset($res->data->items) && !empty($res->data->items)) {
                            $status = 1;
                        }
                        CrmData::updateOrCreate(
                            [
                                'created_at' => $data['fromDate'],
                                'type' => 'company',
                            ],
                            [
                                'portal_id' => $portal->portal_id,
                                'type' => 'company',
                                'from' => 'crm',
                                'data' => json_encode($res->data),
                                'created_at' => $data['fromDate'],
                                'status' => $status
                            ]
                        );
                    } catch (Exception $e) {
                        Log::error("[CrmCompaniesCommand:handle] Exception: " . $e->getMessage());
                    }
                } else {
                    Log::info("[CrmCompaniesCommand:handle] No data for " . $data['fromDate']);
                }
            }
        // }
    }
}
