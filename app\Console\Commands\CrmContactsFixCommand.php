<?php

namespace App\Console\Commands;

use Log;
use Exception;
use App\Helpers\{Crm, Hubspot};
use App\Models\{CrmPortals, CrmData, CrmContact, CrmCompany, CrmCompanyPrimaryReps, CrmEquipment, CrmOwners, CrmEquipmentAll};
use Illuminate\Console\Command;
use Carbon\Carbon;

class CrmContactsFixCommand extends Command
{

    protected $signature = "crm:contactfix";

    protected $description = "Update all hubspot contact owners";

    public function handle()
    {
        $startDate = '2017-01-01';
        $start_time = '00:00:00';
        $endDate = '2025-02-05';
        $end_time = '23:59:59';

        $start_datetime = $startDate . ' ' . $start_time;
        $end_datetime = $endDate . ' ' . $end_time;
        CrmContact::whereBetween('updated_at', [$start_datetime, $end_datetime])->chunkById(200, function ($equipments) {
            foreach ($equipments as $items) {
                $crmId = $items->crm_contact_id ?? "";
                $contactId = $items->hubspot_contact_id ?? "";
                $companyId = $items->crm_company_id ?? "";

                if (!$contactId || !$companyId) {
                    continue;
                }

                $contact_owner = "";
                if (!empty($companyId)) {
                    $getCompanyPrimaryReps = Crm::getCompanyPrimaryReps($companyId);
                    if (!empty($getCompanyPrimaryReps)) {
                        foreach ($getCompanyPrimaryReps as $companyPrimaryReps) {
                            $owner_email = $companyPrimaryReps->email;
                            $role = $companyPrimaryReps->role;
                            if ($role == "Komatsu Rep") {
                                $owner = CrmOwners::where('email', $owner_email)->first();
                                if (isset($owner->owner_id)) {
                                    $contact_owner = $owner->owner_id;
                                }
                                break;
                            }
                        }
                    }
                }

                $props = Hubspot::buildContactProps([
                    'hubspot_owner_id' => $contact_owner
                ]);

                $response = Hubspot::updateContact(722284, $contactId, $props);

                if ($response === false) {
                    Log::error("[CrmContactFixCommand:handle] Contact updation failed: " . json_encode($response));
                    $this->error("contact with id $contactId update unsuccessful");
                    continue;
                } else {
                    try {
                        CrmContact::updateOrCreate(
                            [
                                'hubspot_contact_id' => $contactId,
                                'crm_contact_id'=>$crmId
                            ],
                            [
                                'hubspot_contact_id' => $contactId,
                                'crm_company_id' => $companyId,
                                'contact_owner_id' => $contact_owner,
                                'updated_at' => date('Y-m-d H:i:s'),
                            ]
                        );
                        $this->info("contact with id $contactId updated successfully");
                    } catch (Exception $e) {
                        Log::error("[CrmContactFixCommand:handle] Exception: " . $e->getMessage());
                        $this->error("contact with id $contactId db update unsuccessful");
                    }
                }
            }
        }, $column = 'id');

        $this->info("success");
    }
}
