# Server Setup Guide - Fix "Not Found" Error

## Problem
Your Laravel application is returning "Not Found" errors because Apache isn't properly configured to serve the application from the `/rmscrmseries/` subdirectory.

## Solution Options

### Option 1: Proper Directory Structure (Recommended)

1. **Upload your project to the server:**
   ```
   /var/www/html/rmscrmseries/
   ├── app/
   ├── bootstrap/
   ├── config/
   ├── database/
   ├── public/
   │   ├── index.php
   │   ├── .htaccess
   │   ├── css/
   │   └── js/
   ├── resources/
   ├── routes/
   ├── storage/
   ├── vendor/
   └── .env
   ```

2. **Create/update the root .htaccess file:**
   Place the `root-.htaccess` file as `/var/www/html/.htaccess`

3. **Set proper permissions:**
   ```bash
   sudo chown -R www-data:www-data /var/www/html/rmscrmseries/
   sudo chmod -R 755 /var/www/html/rmscrmseries/
   sudo chmod -R 775 /var/www/html/rmscrmseries/storage/
   sudo chmod -R 775 /var/www/html/rmscrmseries/bootstrap/cache/
   ```

### Option 2: Apache Virtual Host Configuration

1. **Create a new Apache site configuration:**
   ```bash
   sudo nano /etc/apache2/sites-available/rmscrmseries.conf
   ```

2. **Use the content from `apache-config.conf`** (adjust paths as needed)

3. **Enable the site:**
   ```bash
   sudo a2ensite rmscrmseries.conf
   sudo systemctl reload apache2
   ```

### Option 3: Symbolic Link Approach

1. **Create a symbolic link in the web root:**
   ```bash
   sudo ln -s /path/to/your/laravel/project/public /var/www/html/rmscrmseries
   ```

2. **Ensure Apache follows symbolic links:**
   Add to your Apache configuration:
   ```apache
   <Directory /var/www/html>
       Options FollowSymLinks
       AllowOverride All
   </Directory>
   ```

## Testing Steps

After implementing any of the above solutions:

1. **Test basic access:**
   ```
   https://api.niswey.net/rmscrmseries/
   ```

2. **Test Laravel routes:**
   ```
   https://api.niswey.net/rmscrmseries/hubauth
   https://api.niswey.net/rmscrmseries/test
   ```

3. **Check if assets load:**
   ```
   https://api.niswey.net/rmscrmseries/css/app.css
   https://api.niswey.net/rmscrmseries/js/app.js
   ```

## Debugging Steps

### 1. Check Apache Error Logs
```bash
sudo tail -f /var/log/apache2/error.log
```

### 2. Test with Simple HTML File
Create `/var/www/html/rmscrmseries/test.html`:
```html
<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body><h1>Test Page Works!</h1></body>
</html>
```

Access: `https://api.niswey.net/rmscrmseries/test.html`

### 3. Check PHP Processing
Create `/var/www/html/rmscrmseries/phpinfo.php`:
```php
<?php phpinfo(); ?>
```

Access: `https://api.niswey.net/rmscrmseries/phpinfo.php`

### 4. Verify Laravel Installation
```bash
cd /path/to/your/laravel/project
php artisan --version
```

## Common Issues and Solutions

### Issue: "Internal Server Error"
- Check Apache error logs
- Verify .htaccess syntax
- Ensure mod_rewrite is enabled: `sudo a2enmod rewrite`

### Issue: "Permission Denied"
- Set correct ownership: `sudo chown -R www-data:www-data /path/to/project`
- Set correct permissions: `sudo chmod -R 755 /path/to/project`

### Issue: Assets not loading
- Check if files exist in `public/css/` and `public/js/`
- Verify file permissions
- Check browser network tab for 404 errors

### Issue: Database connection errors
- Update `.env` file with correct database credentials
- Test database connection: `php artisan tinker` then `DB::connection()->getPdo()`

## Required Apache Modules

Ensure these modules are enabled:
```bash
sudo a2enmod rewrite
sudo a2enmod ssl
sudo systemctl restart apache2
```

## File Permissions Summary

```bash
# Set ownership
sudo chown -R www-data:www-data /var/www/html/rmscrmseries/

# Set directory permissions
find /var/www/html/rmscrmseries/ -type d -exec chmod 755 {} \;

# Set file permissions
find /var/www/html/rmscrmseries/ -type f -exec chmod 644 {} \;

# Set writable directories
sudo chmod -R 775 /var/www/html/rmscrmseries/storage/
sudo chmod -R 775 /var/www/html/rmscrmseries/bootstrap/cache/
```

Choose the option that best fits your server setup and hosting environment.
