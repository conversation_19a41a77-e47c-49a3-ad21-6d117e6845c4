# Apache Virtual Host Configuration for RMS CRM Series
# Place this in your Apache sites-available directory

<VirtualHost *:80>
    ServerName api.niswey.net
    DocumentRoot /var/www/html
    
    # Redirect HTTP to HTTPS
    Redirect permanent / https://api.niswey.net/
</VirtualHost>

<VirtualHost *:443>
    ServerName api.niswey.net
    DocumentRoot /var/www/html
    
    # SSL Configuration (adjust paths as needed)
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    # Main site configuration
    <Directory /var/www/html>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Laravel application in subdirectory
    Alias /rmscrmseries /path/to/your/laravel/project/public
    
    <Directory /path/to/your/laravel/project/public>
        AllowOverride All
        Require all granted
        
        # Enable rewrite engine
        RewriteEngine On
        
        # Handle Laravel routing
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # Optional: Deny access to sensitive directories
    <Directory /path/to/your/laravel/project>
        <Files ".env">
            Require all denied
        </Files>
        <DirectoryMatch "^.*/\.(git|svn)/">
            Require all denied
        </DirectoryMatch>
    </Directory>
</VirtualHost>
