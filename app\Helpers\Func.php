<?php 

namespace App\Helpers;

use GuzzleHttp\Client;
use App\Helpers\Hubspot;
use Illuminate\Support\Str;

class Func {

	static function handleHourly($data, $count) {
		if(!$data) return $data;

		$processed = [];
		foreach ($data as $key => $values) {
			$valueCount = count($values);
			$value = round(array_sum($values)/$valueCount);
			$processed[] = [$key, $value];
		}
		return $processed;
	}

	static function countryCodes() {
		$codes = [1 => 'US', 11 => 'AG', 12 => 'AI', 13 => 'AS', 14 => 'BB', 15 => 'BM', 16 => 'BS', 17 => 'CA', 18 => 'DM', 19 => 'DO', 110 => 'GD', 111 => 'GU', 112 => 'JM', 113 => 'KN', 114 => 'KY', 115 => 'LC', 116 => 'MP', 117 => 'MS', 118 => 'PR', 119 => 'SX', 120 => 'TC', 121 => 'TT', 122 => 'VC', 123 => 'VG', 124 => 'VI', 7 => 'RU', 71 => 'KZ', 20 => 'EG', 27 => 'ZA', 30 => 'GR', 31 => 'NL', 32 => 'BE', 33 => 'FR', 34 => 'ES', 36 => 'HU', 39 => 'IT', 391 => 'VA', 40 => 'RO', 41 => 'CH', 43 => 'AT', 44 => 'GB', 441 => 'GG', 442 => 'IM', 443 => 'JE', 45 => 'DK', 46 => 'SE', 47 => 'NO', 471 => 'SJ', 48 => 'PL', 49 => 'DE', 51 => 'PE', 52 => 'MX', 53 => 'CU', 54 => 'AR', 55 => 'BR', 56 => 'CL', 57 => 'CO', 58 => 'VE', 60 => 'MY', 61 => 'AU', 611 => 'CC', 612 => 'CX', 62 => 'ID', 63 => 'PH', 64 => 'NZ', 65 => 'SG', 66 => 'TH', 81 => 'JP', 82 => 'KR', 84 => 'VN', 86 => 'CN', 90 => 'TR', 91 => 'IN', 92 => 'PK', 93 => 'AF', 94 => 'LK', 95 => 'MM', 98 => 'IR', 211 => 'SS', 212 => 'MA', 2121 => 'EH', 213 => 'DZ', 216 => 'TN', 218 => 'LY', 220 => 'GM', 221 => 'SN', 222 => 'MR', 223 => 'ML', 224 => 'GN', 225 => 'CI', 226 => 'BF', 227 => 'NE', 228 => 'TG', 229 => 'BJ', 230 => 'MU', 231 => 'LR', 232 => 'SL', 233 => 'GH', 234 => 'NG', 235 => 'TD', 236 => 'CF', 237 => 'CM', 238 => 'CV', 239 => 'ST', 240 => 'GQ', 241 => 'GA', 242 => 'CG', 243 => 'CD', 244 => 'AO', 245 => 'GW', 246 => 'IO', 247 => 'AC', 248 => 'SC', 249 => 'SD', 250 => 'RW', 251 => 'ET', 252 => 'SO', 253 => 'DJ', 254 => 'KE', 255 => 'TZ', 256 => 'UG', 257 => 'BI', 258 => 'MZ', 260 => 'ZM', 261 => 'MG', 262 => 'RE', 2621 => 'YT', 263 => 'ZW', 264 => 'NA', 265 => 'MW', 266 => 'LS', 267 => 'BW', 268 => 'SZ', 269 => 'KM', 290 => 'SH', 2901 => 'TA', 291 => 'ER', 297 => 'AW', 298 => 'FO', 299 => 'GL', 350 => 'GI', 351 => 'PT', 352 => 'LU', 353 => 'IE', 354 => 'IS', 355 => 'AL', 356 => 'MT', 357 => 'CY', 358 => 'FI', 3581 => 'AX', 359 => 'BG', 370 => 'LT', 371 => 'LV', 372 => 'EE', 373 => 'MD', 374 => 'AM', 375 => 'BY', 376 => 'AD', 377 => 'MC', 378 => 'SM', 380 => 'UA', 381 => 'RS', 382 => 'ME', 383 => 'XK', 385 => 'HR', 386 => 'SI', 387 => 'BA', 389 => 'MK', 420 => 'CZ', 421 => 'SK', 423 => 'LI', 500 => 'FK', 501 => 'BZ', 502 => 'GT', 503 => 'SV', 504 => 'HN', 505 => 'NI', 506 => 'CR', 507 => 'PA', 508 => 'PM', 509 => 'HT', 590 => 'GP', 5901 => 'BL', 5902 => 'MF', 591 => 'BO', 592 => 'GY', 593 => 'EC', 594 => 'GF', 595 => 'PY', 596 => 'MQ', 597 => 'SR', 598 => 'UY', 599 => 'CW', 5991 => 'BQ', 670 => 'TL', 672 => 'NF', 673 => 'BN', 674 => 'NR', 675 => 'PG', 676 => 'TO', 677 => 'SB', 678 => 'VU', 679 => 'FJ', 680 => 'PW', 681 => 'WF', 682 => 'CK', 683 => 'NU', 685 => 'WS', 686 => 'KI', 687 => 'NC', 688 => 'TV', 689 => 'PF', 690 => 'TK', 691 => 'FM', 692 => 'MH', 800 => '001', 808 => '001', 850 => 'KP', 852 => 'HK', 853 => 'MO', 855 => 'KH', 856 => 'LA', 870 => '001', 878 => '001', 880 => 'BD', 881 => '001', 882 => '001', 883 => '001', 886 => 'TW', 888 => '001', 960 => 'MV', 961 => 'LB', 962 => 'JO', 963 => 'SY', 964 => 'IQ', 965 => 'KW', 966 => 'SA', 967 => 'YE', 968 => 'OM', 970 => 'PS', 971 => 'AE', 972 => 'IL', 973 => 'BH', 974 => 'QA', 975 => 'BT', 976 => 'MN', 977 => 'NP', 979 => '001', 992 => 'TJ', 993 => 'TM', 994 => 'AZ', 995 => 'GE', 996 => 'KG', 998 => 'UZ'];
		return $codes;
	}

	static function parsePhone($phone) {
		$phone = ltrim($phone, '0');
		$parsePhone = preg_replace("/[^0-9]/", "", $phone);
		Str::startsWith($phone, '+') && $parsePhone = '+'.$parsePhone;
		return $parsePhone;
	}

	static function makeChatId($phone) {
		$phone = self::parsePhone($phone);
		$phone = str_replace('+', '', $phone);
		return $phone;
	}

	static function countryByPhone($phone, $wantCode = false) {
		try {
			$phone = self::parsePhone($phone);
			$phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
			$mobile = $phoneUtil->parse("+".$phone);
			$phone = $phoneUtil->getRegionCodeForNumber($mobile) ?? '';
			$phone && $wantCode && $phone = $phoneUtil->getCountryCodeForRegion($phone);
		} catch(\Exception $e) {
			\Log::error("Invalid phone number provided Exception\n".$e->getMessage());
			$phone = '';
		}
		return $phone;
	}

	static function getNationalNumber($phone) {
		try {
			$phone = self::parsePhone($phone);
			$phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
			$mobile = $phoneUtil->parse("+".$phone);
			$regionCode = $phoneUtil->getRegionCodeForNumber($mobile) ?? '';
			$phone = $phoneUtil->parseAndKeepRawInput('+'.$phone, $regionCode);
			return $phone->getNationalNumber();
		} catch(\Exception $e) {
			\Log::error("Invalid phone number provided Exception\n".$e->getMessage());
			$phone = '';
		}
		return $phone;
	}

	public static function getInternationNumber($phone) {
		$internationNumber = '';
		try {
			$phone = self::parsePhone($phone);
			$phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
			$mobile = $phoneUtil->parse("+".$phone);
			$internationNumber = $phoneUtil->format($mobile, 1);
			return $internationNumber;
		} catch(\Exception $e) {
			\Log::error("Invalid phone number provided Exception\n".$e->getMessage());
			return $internationNumber;
		}
	}

	public static function applyCountryCode($phone, $countryCode) {
		$phone = self::parsePhone($phone);
		$countryCode = str_replace('+', '', $countryCode);

		$codes = self::countryCodes();
		$codeValue = $codes[$countryCode] ?? '';
		if(!$codeValue) return $phone;

		$phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
		$isNumber = $phoneUtil->isPossibleNumber($phone, $codeValue);
		if(!$isNumber) {
			$codeValue = null;
			$phone = '+'.$phone;
		}
		$numberWithCountry = $phoneUtil->parse($phone, $codeValue);
		$phone = $phoneUtil->format($numberWithCountry, \libphonenumber\PhoneNumberFormat::E164);
		// \Log::info("[CRM] countryCode::$countryCode CodeValue::$codeValue IsNumber::$isNumber numberwithCountry::$numberWithCountry finalPhone::$phone");
		return $phone;
	}

	public static function curlRequest($type, $url, $args = []) {
		$curl = curl_init();
		curl_setopt_array($curl, array(
		    CURLOPT_URL => $url,
		    CURLOPT_RETURNTRANSFER => true,
		    CURLOPT_ENCODING => "",
		    CURLOPT_TIMEOUT => 30000,
		    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		    CURLOPT_CUSTOMREQUEST => $type,
		    CURLOPT_HTTPHEADER => $args,
		));
		$response = curl_exec($curl);
		$err = curl_error($curl);
		curl_close($curl);
		return json_decode($response);
	}

	public static function hubspotRequest($type, $url, $args = [], $headers = []) {
		$curl = curl_init();
	    $fields = '';
	    $args && $fields = http_build_query($args);
	    $curlOptions = [
	      CURLOPT_URL => $url,
	      CURLOPT_RETURNTRANSFER => true,
	      CURLOPT_ENCODING => '',
	      CURLOPT_MAXREDIRS => 10,
	      CURLOPT_TIMEOUT => 0,
	      CURLOPT_FOLLOWLOCATION => true,
	      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	      CURLOPT_CUSTOMREQUEST => $type,
	      CURLOPT_HTTPHEADER => $headers,
	    ];
	    $fields && $curlOptions[CURLOPT_POSTFIELDS] = $fields;
	    curl_setopt_array($curl, $curlOptions);
	    $response = curl_exec($curl);
	    curl_close($curl);
	    return json_decode($response);
	}

	public static function request($type, $url, $args = []) {
		$client = new Client(['http_errors' => false]);
		try {
			$response = $client->request($type, $url, $args);
			$res = json_decode($response->getBody());
			// print_r($response); exit;

		} catch(\GuzzleHttp\Exception\RequestException $e ) {
			// echo \GuzzleHttp\Psr7\Message::toString($e->getRequest());
		    if ($e->hasResponse()) {
		        $res = \GuzzleHttp\Psr7\Message::toString($e->getResponse());
		    }
			// $response = $e->getResponse();
			// $res = json_decode($response->getBody()->getContents());
			// \Log::error("HTTP ERROR: for $url");
		} catch(\GuzzleHttp\Exception\ClientException $e ) {
			$response = $e->getResponse();
			$res = json_decode($response->getBody()->getContents());
		}
		return $res;
	}

	static function uploadFile($file) { 
		$path = env('UPLOAD_PATH');
		$fileExt = ".".$file->getClientOriginalExtension(); 
		$fileName = time().$fileExt;

		try {
			$file->move($path, $fileName);
			return env('APP_URL')."public/uploads/".$fileName;
		} catch(\Exception $e) {
			echo $e->getMessage();
			return false;
		}
	}

	static function uploadMultipleFiles($files) {
		$uploaded = [];
		$path = env('UPLOAD_PATH');

		foreach($files as $file) {
			$fileExt = ".".$file->getClientOriginalExtension();
			$fileName = time().$fileExt;

			try {
				$file->move($path, $fileName);
				$uploaded[] = env('APP_URL')."uploads/".$fileName;
			} catch(\Exception $e) {
				\Log::error("[Func:uploadMultipleFiles] Exception: ".$e->getMessage());
			}
			return $uploaded;
		}
	}

	static function typeExt($type) {
		$mime = [
			'audio/aac' => 'aac',
			'text/csv' => 'csv',
			'application/msword' => 'doc',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
			'image/vnd.microsoft.icon' => 'ico',
			'image/gif' => 'gif',
			'image/jpeg' => 'jpeg',
			'image/jpg' => 'jpg',
			'image/png' => 'png',
			'application/json' => 'json',
			'audio/mpeg' => 'mp3',
			'video/mpeg' => 'mpeg',
			'application/pdf' => 'pdf',
			'application/vnd.ms-powerpoint' => 'ppt',
			'text/plain' => 'txt',
			'application/xml' => 'xml',
			'application/zip' => 'zip'
		];
		return $mime[$type] ?? false;
	}

	static function sortMessages($messages, $desc = false) {
		$sorted_messages = [];
		$count = 1;
		foreach($messages as $message) {
			$time = $message->time;
			if(!@$sorted_messages[$time]) {
				$sorted_messages[$time] = $message;
			} else {
				$message->added_seconds = $count;
				$sorted_messages[$time+$count] = $message;
				$count++;
			}
		}

		if($desc) {
			krsort($sorted_messages);
		} else {
			ksort($sorted_messages);
		}
		return $sorted_messages;
	}

	static function isHistory($message) {
		if( (time() - $message['time']) > 600) {
			return true;
		}  else {
			return false;
		}
	}

	static function getMsgBody($message) {
		$show = '';
	    switch ($message->type) {
	        case 'image':
	            if(property_exists($message, 'caption')){
	                $show .= "$message->caption \n\n";
	            }
	            $show.= "**Image:** [View image]($message->body)";
				break;

			case 'document':
				$show = "[View Document]($message->body)";
				break;

			case 'audio':
				$show = "[View Audio]($message->body)";
				break;

			case 'voice':
				$show = "[View Audio]($message->body)";
				break;

			case 'video':
				$show = "[View Video]($message->body)";
				break;

			case 'application':
				$show = "[View Document]($message->body)";
				break;

			default:
				$show = $message->body;
				break;
		}
		return $show;
	}

	static function makePr($properties) {
		$vars = [
			'property' => $properties
		];
		$query = http_build_query($vars, null, '&');
		$string = preg_replace('/%5B(?:[0-9]|[1-9][0-9]+)%5D=/', '=', $query);
		return $string;
	}

	static function formatTemplatePattern($pattern) {
		$pattern = preg_replace('/\n/', '<br>', $pattern);
		$pattern = preg_replace('/ /', '&nbsp;', $pattern);
		// $pattern = preg_replace('/"/', '&quot;', $pattern);
		// $pattern = preg_replace("/'/", '&apos;', $pattern);
		// $withSpaces = preg_replace();
		// $withTabs = preg_replace();
		
		return $pattern;
	}

	static function applyMsgTemplate($pattern, $user, $portalId) {
		if(!is_array($user)) $user = (array) $user;

		preg_match_all('/{\w+.\w+}/', $pattern, $tokens);
		if(!$tokens || !$tokens[0]) return $pattern;
		$oldPatternKeys = ['first_name', 'last_name', 'full_name'];
		foreach ($tokens[0] as $token) {
			$propertyKey = preg_replace('/[{}]/', '', $token);
			if(strpos($propertyKey, ".")){
				list($objType, $propertyKey) = explode(".",$propertyKey);
			}else{
				$objType = null;
			}
			if($objType == "OWNER"){
				$owner = Hubspot::ownerInfo($user[$propertyKey], $portalId);
				if($owner){
					$firstName = $owner->firstName ?? "";
					$lastName = $owner->lastName ?? "";
					$user[$propertyKey] = $firstName." ".$lastName;
				}
			}
			if(in_array($propertyKey, $oldPatternKeys)) {
				$propertyKey = str_replace('_', '', $propertyKey);
			}
			isset($user[$propertyKey]) && $pattern = preg_replace("/$token/", $user[$propertyKey], $pattern);
		}
		return $pattern;
	}

	static function buildConversationsV1($messages, $dialogs) {
		$messageIndexing = [];
		foreach ($dialogs as $dialog) {
			$messageIndexing[$dialog->id] = $dialog;
			$messageIndexing[$dialog->id]->time = 0;
		}

		foreach ($messages as $message) {
			$message->body = self::makeMessageBody($message);
			$messageIndexing[$message->chatId]->messages[] = $message;
			$messageIndexing[$message->chatId]->time = $message->time;
			$phone = $message->fromMe
			? $phone = explode("@", $message->chatId)[0]
			: $phone = explode("@", $message->author)[0];
			$messageIndexing[$message->chatId]->phone = $phone;
			$messageIndexing[$message->chatId]->id = $phone.'@c.us';
		}
		
		// sort based on time
		usort($messageIndexing, function($a, $b) { 
			return $a->time < $b->time;
		});
		return $messageIndexing;
	}

	static function buildConversations($messages) {
		$conversations = [];
		foreach ($messages as $message) {
			$chatId = explode('@', $message->chatId)[0];
			$message->body = self::makeMessageBody($message);
			$conversations[$chatId][] = $message;
		}
		return $conversations;
	}

	static function buildDialogs($messages) {
		$dialogs = [];
		foreach ($messages as $message) {
			$chatId = $message->chatId;

			$name = "";
			$phone = explode("@", $message->chatId)[0];
			$message->body = self::makeMessageBody($message);

			if(!$message->fromMe) {
				$phone = explode("@", $message->author)[0];
				$name = ($message->senderName != 'undefined' || $message->senderName)
				? $message->senderName
				: $message->chatName;
			}

			if(strpos($chatId, 'g.us') !== false) {
				$name = $message->chatName;
			}

			$dialog = [
				'id' => $chatId,
				'phone' => '+'.$phone,
				'time' => $message->time,
				'name' => $name
			];

			if(!isset($dialogs[$chatId])) {
				$dialogs[$chatId] = $dialog;
				continue;
			}

			if($dialogs[$chatId]['name'] == ""){
				$dialogs[$chatId]['name'] = $dialog['name'];
			}
			$dialogs[$chatId]['time'] = $dialog['time'];
		}
		return $dialogs;
	}

	static function makeMessageBody($message, $isQuoted = false, $quotedMsgBody = '') {
		if($isQuoted && strlen($message->body) > 199) {
			$nextSpaceIndex = strpos($message->body, ' ', 190);
			if($nextSpaceIndex !== false) {
				$body = substr($message->body, 0, $nextSpaceIndex).'...';
			} else {
				$body = $message->body;
			}
			$message->body = $body;
		}
		switch ($message->type) {
	        case 'image':
	        	// check if image is older than 1 month and return 
	            if((time() - $message->time) > 2592000) {
	            	$img_url = env("APP_URL").'images/not-available.jpeg';	
	            	$show = "<img src='$img_url' />";
	            } else {
		            $show = "<img src='$message->body' />";
	            }
	            if(property_exists($message, 'caption')){
	                $show .= "<br>$message->caption";
	            }
				break;
			case 'video':
				$show = "<video controls><source src='".$message->body."' type='video/mp4'></video>";
	            if(property_exists($message, 'caption')){
	                $show .= "<br>$message->caption";
	            }
				break;
			case 'ptt':
				$show = "<audio controls><source src='".$message->body."' type='audio/ogg'></audio>";
				if(property_exists($message, 'caption')){
	                $show .= "<br>$message->caption";
				}
				break;
			case 'document':
				$show = "<a href= '$message->body'>Document</a>";
				break;

			case 'revoked':
				$show = "<i>this message was deleted</i>";
				break;

			case 'file':
				$show = "<a href='$message->body' target='_blank'>File</a>";
				break;

			default:
				$show = $message->body;
				break;
		}
		if($quotedMsgBody) {
			$msg = '<blockquote class="blockquote">'.$quotedMsgBody.'</blockquote>';
			$show = $msg.$show;
		}
		return $show;
	}

	static function updateDialogs($dialog) {
		try {
			\App\Models\Dialogs::updateOrCreate([
				'id' => $dialog['id'],
				'dialog_id' => $dialog['dialog_id'],
			], $dialog);
		} catch(Exception $e) {
			\Log::error("[Func:updateDialogs] Exception ".$e->getMessage());
		}
	}

	static function makeTwilioMsgBody($conversation) {
		$type = $conversation['type'];
    	$media_url = $conversation['media_url'];

		switch ($type) {
	        case 'image':
	            $show = "<img src='$media_url' />";
	            if($conversation['body']) {
	                $show .= "<br>$conversation[body]";
	            }
				break;
			case 'video':
				$show = "<video controls><source src='".$media_url."' type='video/mp4'></video>";
	            if($conversation['body']) {
	                $show .= "<br>$conversation[body]";
	            }
				break;
			case 'audio':
				$show = "<audio controls><source src='".$media_url."' type='audio/MP3'></audio>";
				if($conversation['body']) {
	                $show .= "<br>$conversation[body]";
	            }
				break;
			case 'voice':
				$show = "<audio controls><source src='".$media_url."' type='audio/MP3'></audio>";
				if($conversation['body']) {
	                $show .= "<br>$conversation[body]";
	            }
				break;
			case 'document':
				$show = "<a href= '$media_url'>$conversation[body]</a>";
				break;
			case 'application':
				$show = "<a href= '$media_url'>$conversation[body]</a>";
				break;
			default:
				$show = $conversation['body'];
				break;
		}
		$conversation['body'] = $show;
		return $conversation;
	}

	static function makeKaleyraMsgBody($message) {
		switch ($message->file_type) {
	        case 'image':
	            $show = "<img src='$message->file_url' />";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'video':
				$show = "<video controls><source src='".$message->file_url."' type='video/mp4'></video>";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'ptt':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'audio':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'voice':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'document':
				$show = "<a href= '$message->file_url'>Document</a>";
				break;
			case 'application':
				$show = "<a href= '$message->file_url'>Document</a>";
				break;
			case 'file':
				$show = "<a href='$message->file_url' target='_blank'>File</a>";
				break;

			default:
				$show = $message->body;
				break;
		}
		return $show;
	}

	static function makeGupshupMsgBody($message) {
		switch ($message->file_type) {
	        case 'image':
	            $show = "<img src='$message->file_url' />";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'video':
				$show = "<video controls><source src='".$message->file_url."' type='video/mp4'></video>";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'audio':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'voice':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'document':
				$show = "<a href= '$message->file_url'>Document</a>";
				break;
			case 'application':
				$show = "<a href= '$message->file_url'>Document</a>";
				break;

			case 'file':
				$show = "<a href='$message->file_url' target='_blank'>File</a>";
				break;

			default:
				$show = $message->body;
				break;
		}
		return $show;
	}

	static function makeClareMsgBody($message) {
		switch ($message->file_type) {
	        case 'image':
	            $show = "<img src='$message->file_url' />";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'video':
				$show = "<video controls><source src='".$message->file_url."' type='video/mp4'></video>";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'audio':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'voice':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'document':
				$show = "<a href= '$message->file_url'>Document</a>";
				break;
			case 'application':
				$show = "<a href= '$message->file_url'>Document</a>";
				break;
			case 'file':
				$show = "<a href='$message->file_url' target='_blank'>File</a>";
				break;

			default:
				$show = $message->body;
				break;
		}
		return $show;
	}

	static function clareMediaBody($file, $body, $type) {
		switch ($type) {
	        case 'image':
	            $show = "<img src='$file' />";
	            if($body) $show .= "<br>$body";
				break;
			case 'video':
				$show = "<video controls><source src='".$file."' type='video/mp4'></video>";
				if($body) $show .= "<br>$body";
				break;
			case 'ptt':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'document':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'application':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'file':
				$show = "<a href='$file' target='_blank'>File</a>";
				if($body) $show .= "<br>$body";
				break;

			default:
				$show = $body;
				break;
		}
		return $show;
	}

	static function kaleyraMediaBody($file, $body, $type) {
		switch ($type) {
	        case 'image':
	            $show = "<img src='$file' />";
	            if($body) $show .= "<br>$body";
				break;
			case 'video':
				$show = "<video controls><source src='".$file."' type='video/mp4'></video>";
				if($body) $show .= "<br>$body";
				break;
			case 'ptt':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'document':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'application':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'file':
				$show = "<a href='$file' target='_blank'>File</a>";
				if($body) $show .= "<br>$body";
				break;

			default:
				$show = $body;
				break;
		}
		return $show;
	}

	static function makeInfobipMsgBody($message) {
		switch ($message->file_type) {
	        case 'image':
	            $show = "<img src='$message->file_url' />";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'video':
				$show = "<video controls><source src='".$message->file_url."' type='video/mp4'></video>";
	            if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'audio':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'voice':
				$show = "<audio controls><source src='".$message->file_url."' type='audio/ogg'></audio>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'document':
				$show = "<a href= '$message->file_url'>Document</a>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'application':
				$show = "<a href= '$message->file_url'>Document</a>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;
			case 'file':
				$show = "<a href='$message->file_url' target='_blank'>File</a>";
				if($message->body){
	                $show .= "<br>$message->body";
	            }
				break;

			default:
				$show = $message->body;
				break;
		}
		return $show;
	}

	static function infobipMediaBody($file, $body, $type) {
		switch ($type) {
	        case 'image':
	            $show = "<img src='$file' />";
	            if($body) $show .= "<br>$body";
				break;
			case 'video':
				$show = "<video controls><source src='".$file."' type='video/mp4'></video>";
				if($body) $show .= "<br>$body";
				break;
			case 'ptt':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'audio':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'voice':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'document':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'application':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'text':
				$show = $body;
				break;

			default:
				$show = "<a href='$file' target='_blank'>File</a>";
				if($body) $show .= "<br>$body";
				break;
		}
		return $show;
	}

	static function twilioMediaBody($file, $body, $type) {
		switch ($type) {
	        case 'image':
	            $show = "<img src='$file' />";
	            if($body) $show .= "<br>$body";
				break;
			case 'video':
				$show = "<video controls><source src='".$file."' type='video/mp4'></video>";
				if($body) $show .= "<br>$body";
				break;
			case 'ptt':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'audio':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'voice':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'document':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'application':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;

			case 'file':
				$show = "<a href='$file' target='_blank'>File</a>";
				if($body) $show .= "<br>$body";
				break;

			default:
				$show = $body;
				break;
		}
		return $show;
	}

	static function gupshupMediaBody($file, $body, $type) {
		switch ($type) {
	        case 'image':
	            $show = "<img src='$file' />";
	            if($body) $show .= "<br>$body";
				break;
			case 'video':
				$show = "<video controls><source src='".$file."' type='video/mp4'></video>";
				if($body) $show .= "<br>$body";
				break;
			case 'ptt':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'audio':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'voice':
				$show = "<audio controls><source src='".$file."' type='audio/ogg'></audio>";
				if($body) $show .= "<br>$body";
				break;
			case 'document':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'application':
				$show = "<a href= '$file'>Document</a>";
				if($body) $show .= "<br>$body";
				break;
			case 'file':
				$show = "<a href='$file' target='_blank'>File</a>";
				if($body) $show .= "<br>$body";
				break;

			default:
				$show = $body;
				break;
		}
		return $show;
	}
}
